---
description: 
globs: 
alwaysApply: true
---

## 总体原则

***综合检索：** 所有提问都必须首先检索项目文件夹中的所有相关文档，以获取上下文。
***网络搜索：** 检索本地文件后，必须进行网络搜索，以获取更多信息和最新数据。
修改代码前先确定被修改的代码不会影响到 app其他的功能

## MCP 使用

使用以下 MCP 模型上下文协议 来增强响应：
**sequential-thinking mcp：**
* 用途： 将复杂的问题分解为更小的、更易于管理的部分，并逐步推理以确保答案的逻辑性和连贯性。
* 触发条件： 当用户提出需要多个步骤或依赖于逻辑推理的问题时使用。这有助于提供更清晰、更有条理的响应。

## 示例工作流程
用户提出问题。
Cursor 检索项目文件夹中的相关文件。
Cursor 在网上搜索相关信息。
Cursor 使用 sequential-thinking mcp 将问题分解为更小的部分，并应用逻辑推理。
Cursor 以清晰且有条理的格式向用户提供最终答案。