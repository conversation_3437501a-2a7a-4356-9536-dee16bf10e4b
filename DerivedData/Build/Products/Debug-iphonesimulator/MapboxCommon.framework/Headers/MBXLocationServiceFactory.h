// This file is generated and will be overwritten automatically.

#import <Foundation/Foundation.h>

@protocol MBXLocationService;

/**
 * Location service factory.
 *
 * This class is used to instantiate a platform-specific implementation
 * of location service.
 *
 */
NS_SWIFT_NAME(LocationServiceFactory)
__attribute__((visibility ("default")))
@interface MBXLocationServiceFactory : NSObject

// This class provides custom init which should be called
- (nonnull instancetype)init NS_UNAVAILABLE;

// This class provides custom init which should be called
+ (nonnull instancetype)new NS_UNAVAILABLE;


@end
