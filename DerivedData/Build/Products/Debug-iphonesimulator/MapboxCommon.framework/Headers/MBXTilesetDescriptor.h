// This file is generated and will be overwritten automatically.

#import <Foundation/Foundation.h>

/**
 * A bundle that encapsulates tilesets creation for the tile store implementation.
 *
 * Tileset descriptors describe the type of data that should be part of the Offline Region, like the routing profile for Navigation and the Tilesets of the Map style.
 */
NS_SWIFT_NAME(TilesetDescriptor)
__attribute__((visibility ("default")))
@interface MBXTilesetDescriptor : NSObject

// This class provides custom init which should be called
- (nonnull instancetype)init NS_UNAVAILABLE;

// This class provides custom init which should be called
+ (nonnull instancetype)new NS_UNAVAILABLE;


@end
