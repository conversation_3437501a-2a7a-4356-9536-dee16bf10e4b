// This file is generated and will be overwritten automatically.

#import <Foundation/Foundation.h>
@class MBXExpected<__covariant Value, __covariant Error>;

/**
 * Platform independent settings service. Settings service is a key/value storage (persistent or non
 * persistent), that also allows to register observers to get notifications when values are updated.
 */
NS_SWIFT_NAME(SettingsService)
__attribute__((visibility ("default")))
@interface MBXSettingsService : NSObject

// This class provides custom init which should be called
- (nonnull instancetype)init NS_UNAVAILABLE;

// This class provides custom init which should be called
+ (nonnull instancetype)new NS_UNAVAILABLE;


@end
