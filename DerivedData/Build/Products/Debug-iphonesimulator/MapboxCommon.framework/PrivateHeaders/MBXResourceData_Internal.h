// This file is generated and will be overwritten automatically.

#import <Foundation/Foundation.h>
@class MBXDataRef;

NS_SWIFT_NAME(ResourceData)
__attribute__((visibility ("default")))
@interface MBXResourceData : NSObject

// This class provides custom init which should be called
- (nonnull instancetype)init NS_UNAVAILABLE;

// This class provides custom init which should be called
+ (nonnull instancetype)new NS_UNAVAILABLE;

- (nullable MBXDataRef *)getData __attribute((ns_returns_retained));

@end
