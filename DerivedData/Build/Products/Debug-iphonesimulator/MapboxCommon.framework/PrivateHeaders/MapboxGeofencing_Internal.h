// This file is generated and will be overwritten automatically.

#import "MBXExperimentalGeofencingErrorType_Internal.h"
#import "MBXExperimentalGeofencingError_Internal.h"
#import "MBXExperimentalGeofencingEvent_Internal.h"
#import "MBXExperimentalGeofenceState_Internal.h"
#import "MBXExperimentalGeofencingOptions_Internal.h"
#import "MBXExperimentalGeofencingFactory_Internal.h"
#import "MBXExperimentalGeofencingUtils_Internal.h"
#import "MBXExperimentalGeofencingObserver_Internal.h"
#import "MBXExperimentalGeofencingService_Internal.h"
#import "MBXExperimentalAddFeatureCallback_Internal.h"
#import "MBXExperimentalGetFeatureCallback_Internal.h"
#import "MBXExperimentalRemoveFeatureCallback_Internal.h"
#import "MBXExperimentalClearFeaturesCallback_Internal.h"
#import "MBXExperimentalAddObserverCallback_Internal.h"
#import "MBXExperimentalRemoveObserverCallback_Internal.h"
#import "MBXExperimentalConfigureCallback_Internal.h"
#import "MBXExperimentalGetOptionsCallback_Internal.h"
#import "MBXExperimentalGeofencingUtilsUserConsentResponseCallback_Internal.h"
#import "MBXExperimentalGeofencingPropertiesKeys_Internal.h"
