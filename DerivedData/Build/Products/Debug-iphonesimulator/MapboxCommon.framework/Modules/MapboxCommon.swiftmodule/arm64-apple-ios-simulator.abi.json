{"ABIRoot": {"kind": "Root", "name": "MapboxCommon", "printedName": "MapboxCommon", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "TypeDecl", "name": "DataRef", "printedName": "DataRef", "children": [{"kind": "Var", "name": "data", "printedName": "data", "children": [{"kind": "TypeNominal", "name": "Data", "printedName": "Foundation.Data", "usr": "s:10Foundation4DataV"}], "declKind": "Var", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef(py)data", "mangledName": "$s12MapboxCommon7DataRefC4data10Foundation0C0Vvp", "moduleName": "MapboxCommon", "declAttributes": ["Final", "HasStorage", "AccessControl", "ObjC"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Data", "printedName": "Foundation.Data", "usr": "s:10Foundation4DataV"}], "declKind": "Accessor", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef(im)data", "mangledName": "$s12MapboxCommon7DataRefC4data10Foundation0C0Vvg", "moduleName": "MapboxCommon", "implicit": true, "declAttributes": ["Final", "ObjC"], "accessorKind": "get"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(data:)", "children": [{"kind": "TypeNominal", "name": "DataRef", "printedName": "MapboxCommon.DataRef", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef"}, {"kind": "TypeNominal", "name": "Data", "printedName": "Foundation.Data", "usr": "s:10Foundation4DataV"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef(im)initWithData:", "mangledName": "$s12MapboxCommon7DataRefC4dataAC10Foundation0C0V_tcfc", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "ObjC"], "init_kind": "Designated"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init()", "children": [{"kind": "TypeNominal", "name": "DataRef", "printedName": "MapboxCommon.DataRef", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef(im)init", "mangledName": "$s12MapboxCommon7DataRefCACycfc", "moduleName": "MapboxCommon", "overriding": true, "implicit": true, "objc_name": "init", "declAttributes": ["Dynamic", "ObjC", "Override"], "init_kind": "Designated"}], "declKind": "Class", "usr": "c:@M@MapboxCommon@objc(cs)MBXDataRef", "mangledName": "$s12MapboxCommon7DataRefC", "moduleName": "MapboxCommon", "objc_name": "MBXDataRef", "declAttributes": ["AccessControl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ObjC"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "Import", "name": "UIKit", "printedName": "UIKit", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "Import", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly"]}, {"kind": "Import", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["SPIAccessControl"]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofenceState", "printedName": "GeofenceState", "children": [{"kind": "Var", "name": "feature", "printedName": "feature", "children": [{"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}], "declKind": "Var", "usr": "s:12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvp", "mangledName": "$s12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}], "declKind": "Accessor", "usr": "s:12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvg", "mangledName": "$s12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}], "declKind": "Accessor", "usr": "s:12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvs", "mangledName": "$s12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvM", "mangledName": "$s12MapboxCommon13GeofenceStateV7feature4Turf7FeatureVvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "Var", "name": "timestamp", "printedName": "timestamp", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Foundation.Date?", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvp", "mangledName": "$s12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Foundation.Date?", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvg", "mangledName": "$s12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Foundation.Date?", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvs", "mangledName": "$s12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvM", "mangledName": "$s12MapboxCommon13GeofenceStateV9timestamp10Foundation4DateVSgvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(feature:timestamp:)", "children": [{"kind": "TypeNominal", "name": "GeofenceState", "printedName": "MapboxCommon.GeofenceState", "usr": "s:12MapboxCommon13GeofenceStateV"}, {"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Foundation.Date?", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12MapboxCommon13GeofenceStateV7feature9timestampAC4Turf7FeatureV_10Foundation4DateVSgtcfc", "mangledName": "$s12MapboxCommon13GeofenceStateV7feature9timestampAC4Turf7FeatureV_10Foundation4DateVSgtcfc", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl"], "spi_group_names": ["Experimental"], "init_kind": "Designated"}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GeofenceState", "printedName": "MapboxCommon.GeofenceState", "usr": "s:12MapboxCommon13GeofenceStateV"}], "declKind": "Func", "usr": "s:12MapboxCommon13GeofenceStateV10MarshallerV6toObjcyxAClFZ", "mangledName": "$s12MapboxCommon13GeofenceStateV10MarshallerV6toObjcyxAClFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "GeofenceState", "printedName": "MapboxCommon.GeofenceState", "usr": "s:12MapboxCommon13GeofenceStateV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon13GeofenceStateV10MarshallerV7toSwiftyACypFZ", "mangledName": "$s12MapboxCommon13GeofenceStateV10MarshallerV7toSwiftyACypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:12MapboxCommon13GeofenceStateV10MarshallerV", "mangledName": "$s12MapboxCommon13GeofenceStateV10MarshallerV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "isFromExtension": true, "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}], "declKind": "Struct", "usr": "s:12MapboxCommon13GeofenceStateV", "mangledName": "$s12MapboxCommon13GeofenceStateV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingError", "printedName": "GeofencingError", "children": [{"kind": "Var", "name": "type", "printedName": "type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}], "declKind": "Var", "usr": "s:12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvp", "mangledName": "$s12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl", "SPIAccessControl"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvg", "mangledName": "$s12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvs", "mangledName": "$s12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvM", "mangledName": "$s12MapboxCommon15GeofencingErrorV4typeAA0cD4TypeOvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "Var", "name": "message", "printedName": "message", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:12MapboxCommon15GeofencingErrorV7messageSSvp", "mangledName": "$s12MapboxCommon15GeofencingErrorV7messageSSvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingErrorV7messageSSvg", "mangledName": "$s12MapboxCommon15GeofencingErrorV7messageSSvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingErrorV7messageSSvs", "mangledName": "$s12MapboxCommon15GeofencingErrorV7messageSSvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingErrorV7messageSSvM", "mangledName": "$s12MapboxCommon15GeofencingErrorV7messageSSvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(type:message:)", "children": [{"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}, {"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12MapboxCommon15GeofencingErrorV4type7messageAcA0cD4TypeO_SStcfc", "mangledName": "$s12MapboxCommon15GeofencingErrorV4type7messageAcA0cD4TypeO_SStcfc", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Experimental"], "init_kind": "Designated"}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingErrorV10MarshallerV6toObjcyxAClFZ", "mangledName": "$s12MapboxCommon15GeofencingErrorV10MarshallerV6toObjcyxAClFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingErrorV10MarshallerV7toSwiftyACypFZ", "mangledName": "$s12MapboxCommon15GeofencingErrorV10MarshallerV7toSwiftyACypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:12MapboxCommon15GeofencingErrorV10MarshallerV", "mangledName": "$s12MapboxCommon15GeofencingErrorV10MarshallerV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "isFromExtension": true, "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}], "declKind": "Struct", "usr": "s:12MapboxCommon15GeofencingErrorV", "mangledName": "$s12MapboxCommon15GeofencingErrorV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Error", "printedName": "Error", "usr": "s:s5ErrorP", "mangledName": "$ss5ErrorP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingErrorType", "printedName": "GeofencingErrorType", "children": [{"kind": "Var", "name": "generic", "printedName": "generic", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO7genericyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO7genericyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "notAvailable", "printedName": "notAvailable", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO12notAvailableyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO12notAvailableyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "defaultRadiusTooLarge", "printedName": "defaultRadiusTooLarge", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO21defaultRadiusTooLargeyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO21defaultRadiusTooLargeyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "monitoredFeaturesLimitReached", "printedName": "monitoredFeaturesLimitReached", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO29monitoredFeaturesLimitReachedyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO29monitoredFeaturesLimitReachedyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "locationServiceUnavailable", "printedName": "locationServiceUnavailable", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO26locationServiceUnavailableyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO26locationServiceUnavailableyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "locationServiceUnauthorized", "printedName": "locationServiceUnauthorized", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO27locationServiceUnauthorizedyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO27locationServiceUnauthorizedyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "locationServiceInaccurate", "printedName": "locationServiceInaccurate", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO25locationServiceInaccurateyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO25locationServiceInaccurateyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "deviceLocationProviderError", "printedName": "deviceLocationProviderError", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO022deviceLocationProviderD0yA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO022deviceLocationProviderD0yA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "featureAlreadyAdded", "printedName": "featureAlreadyAdded", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO19featureAlreadyAddedyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO19featureAlreadyAddedyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "featureNotFound", "printedName": "featureNotFound", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO15featureNotFoundyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO15featureNotFoundyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "featureInvalid", "printedName": "featureInvalid", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO14featureInvalidyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO14featureInvalidyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "observerAlreadyAdded", "printedName": "observerAlreadyAdded", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO20observerAlreadyAddedyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO20observerAlreadyAddedyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "observerNotFound", "printedName": "observerNotFound", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO16observerNotFoundyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO16observerNotFoundyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "Var", "name": "featureNotStored", "printedName": "featureNotStored", "children": [{"kind": "TypeFunc", "name": "Function", "printedName": "(MapboxCommon.GeofencingErrorType.Type) -> MapboxCommon.GeofencingErrorType", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "MapboxCommon.GeofencingErrorType.Type", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}]}]}], "declKind": "EnumElement", "usr": "s:12MapboxCommon19GeofencingErrorTypeO16featureNotStoredyA2CmF", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO16featureNotStoredyA2CmF", "moduleName": "MapboxCommon", "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(rawValue:)", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "MapboxCommon.GeofencingErrorType?", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}], "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12MapboxCommon19GeofencingErrorTypeO8rawValueACSgSi_tcfc", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO8rawValueACSgSi_tcfc", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "init_kind": "Designated"}, {"kind": "Var", "name": "rawValue", "printedName": "rawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:12MapboxCommon19GeofencingErrorTypeO8rawValueSivp", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO8rawValueSivp", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:12MapboxCommon19GeofencingErrorTypeO8rawValueSivg", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO8rawValueSivg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}]}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}], "declKind": "Func", "usr": "s:12MapboxCommon19GeofencingErrorTypeO10MarshallerV6toObjcyxAClFZ", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO10MarshallerV6toObjcyxAClFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "GeofencingErrorType", "printedName": "MapboxCommon.GeofencingErrorType", "usr": "s:12MapboxCommon19GeofencingErrorTypeO"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon19GeofencingErrorTypeO10MarshallerV7toSwiftyACypFZ", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO10MarshallerV7toSwiftyACypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:12MapboxCommon19GeofencingErrorTypeO10MarshallerV", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO10MarshallerV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "isFromExtension": true, "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}], "declKind": "Enum", "usr": "s:12MapboxCommon19GeofencingErrorTypeO", "mangledName": "$s12MapboxCommon19GeofencingErrorTypeO", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "enumRawTypeName": "Int", "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}]}, {"kind": "Import", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["SPIAccessControl"]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingEvent", "printedName": "GeofencingEvent", "children": [{"kind": "Var", "name": "feature", "printedName": "feature", "children": [{"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}], "declKind": "Var", "usr": "s:12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvp", "mangledName": "$s12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvg", "mangledName": "$s12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvs", "mangledName": "$s12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvM", "mangledName": "$s12MapboxCommon15GeofencingEventV7feature4Turf7FeatureVvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "Var", "name": "timestamp", "printedName": "timestamp", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "declKind": "Var", "usr": "s:12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvp", "mangledName": "$s12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvg", "mangledName": "$s12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvs", "mangledName": "$s12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvM", "mangledName": "$s12MapboxCommon15GeofencingEventV9timestamp10Foundation4DateVvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(feature:timestamp:)", "children": [{"kind": "TypeNominal", "name": "GeofencingEvent", "printedName": "MapboxCommon.GeofencingEvent", "usr": "s:12MapboxCommon15GeofencingEventV"}, {"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}, {"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12MapboxCommon15GeofencingEventV7feature9timestampAC4Turf7FeatureV_10Foundation4DateVtcfc", "mangledName": "$s12MapboxCommon15GeofencingEventV7feature9timestampAC4Turf7FeatureV_10Foundation4DateVtcfc", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl"], "spi_group_names": ["Experimental"], "init_kind": "Designated"}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GeofencingEvent", "printedName": "MapboxCommon.GeofencingEvent", "usr": "s:12MapboxCommon15GeofencingEventV"}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingEventV10MarshallerV6toObjcyxAClFZ", "mangledName": "$s12MapboxCommon15GeofencingEventV10MarshallerV6toObjcyxAClFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "GeofencingEvent", "printedName": "MapboxCommon.GeofencingEvent", "usr": "s:12MapboxCommon15GeofencingEventV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingEventV10MarshallerV7toSwiftyACypFZ", "mangledName": "$s12MapboxCommon15GeofencingEventV10MarshallerV7toSwiftyACypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:12MapboxCommon15GeofencingEventV10MarshallerV", "mangledName": "$s12MapboxCommon15GeofencingEventV10MarshallerV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "isFromExtension": true, "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}], "declKind": "Struct", "usr": "s:12MapboxCommon15GeofencingEventV", "mangledName": "$s12MapboxCommon15GeofencingEventV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingExpectedMarshallers", "printedName": "GeofencingExpectedMarshallers", "children": [{"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOyytAA0C5ErrorVGypFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOyytAA0C5ErrorVGypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOyytAA0C5ErrorVGlFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOyytAA0C5ErrorVGlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.GeofencingOptions, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOyAA0C7OptionsVAA0C5ErrorVGlFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOyAA0C7OptionsVAA0C5ErrorVGlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<Swift.String, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOySSAA0C5ErrorVGlFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOySSAA0C5ErrorVGlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.GeofenceState, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "GeofenceState", "printedName": "MapboxCommon.GeofenceState", "usr": "s:12MapboxCommon13GeofenceStateV"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOyAA13GeofenceStateVAA0C5ErrorVGlFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOyAA13GeofenceStateVAA0C5ErrorVGlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<Swift.UInt32, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOys6UInt32VAA0C5ErrorVGlFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO6toObjcyxs6ResultOys6UInt32VAA0C5ErrorVGlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.GeofencingOptions, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOyAA0C7OptionsVAA0C5ErrorVGypFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOyAA0C7OptionsVAA0C5ErrorVGypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<Swift.String, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOySSAA0C5ErrorVGypFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOySSAA0C5ErrorVGypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.GeofenceState, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "GeofenceState", "printedName": "MapboxCommon.GeofenceState", "usr": "s:12MapboxCommon13GeofenceStateV"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOyAA13GeofenceStateVAA0C5ErrorVGypFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOyAA13GeofenceStateVAA0C5ErrorVGypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<Swift.UInt32, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOys6UInt32VAA0C5ErrorVGypFZ", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO7toSwiftys6ResultOys6UInt32VAA0C5ErrorVGypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Enum", "usr": "s:12MapboxCommon29GeofencingExpectedMarshallersO", "mangledName": "$s12MapboxCommon29GeofencingExpectedMarshallersO", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingFactory", "printedName": "GeofencingFactory", "children": [{"kind": "Function", "name": "getOrCreate", "printedName": "getOrCreate()", "children": [{"kind": "TypeNominal", "name": "GeofencingService", "printedName": "any MapboxCommon.GeofencingService", "usr": "s:12MapboxCommon17GeofencingServiceP"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingFactoryC11getOrCreateAA0C7Service_pyFZ", "mangledName": "$s12MapboxCommon17GeofencingFactoryC11getOrCreateAA0C7Service_pyFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "reset", "printedName": "reset()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingFactoryC5resetyyFZ", "mangledName": "$s12MapboxCommon17GeofencingFactoryC5resetyyFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "setUserDefined", "printedName": "setUserDefined(custom:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingService", "printedName": "any MapboxCommon.GeofencingService", "usr": "s:12MapboxCommon17GeofencingServiceP"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingFactoryC14setUserDefined6customyAA0C7Service_p_tFZ", "mangledName": "$s12MapboxCommon17GeofencingFactoryC14setUserDefined6customyAA0C7Service_p_tFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:12MapboxCommon17GeofencingFactoryC", "mangledName": "$s12MapboxCommon17GeofencingFactoryC", "moduleName": "MapboxCommon", "declAttributes": ["Final", "AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "hasMissingDesignatedInitializers": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingObserver", "printedName": "GeofencingObserver", "children": [{"kind": "Function", "name": "onEntry", "printedName": "onEntry(event:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingEvent", "printedName": "MapboxCommon.GeofencingEvent", "usr": "s:12MapboxCommon15GeofencingEventV"}], "declKind": "Func", "usr": "s:12MapboxCommon18GeofencingObserverP7onEntry5eventyAA0C5EventV_tF", "mangledName": "$s12MapboxCommon18GeofencingObserverP7onEntry5eventyAA0C5EventV_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingObserver>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingObserver>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "onDwell", "printedName": "onDwell(event:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingEvent", "printedName": "MapboxCommon.GeofencingEvent", "usr": "s:12MapboxCommon15GeofencingEventV"}], "declKind": "Func", "usr": "s:12MapboxCommon18GeofencingObserverP7onDwell5eventyAA0C5EventV_tF", "mangledName": "$s12MapboxCommon18GeofencingObserverP7onDwell5eventyAA0C5EventV_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingObserver>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingObserver>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "onExit", "printedName": "onExit(event:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingEvent", "printedName": "MapboxCommon.GeofencingEvent", "usr": "s:12MapboxCommon15GeofencingEventV"}], "declKind": "Func", "usr": "s:12MapboxCommon18GeofencingObserverP6onExit5eventyAA0C5EventV_tF", "mangledName": "$s12MapboxCommon18GeofencingObserverP6onExit5eventyAA0C5EventV_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingObserver>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingObserver>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "onError", "printedName": "onError(error:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "declKind": "Func", "usr": "s:12MapboxCommon18GeofencingObserverP7onError5erroryAA0cF0V_tF", "mangledName": "$s12MapboxCommon18GeofencingObserverP7onError5erroryAA0cF0V_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingObserver>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingObserver>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "onUserConsentChanged", "printedName": "onUserConsentChanged(isConsentGiven:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "s:12MapboxCommon18GeofencingObserverP20onUserConsentChanged02isG5GivenySb_tF", "mangledName": "$s12MapboxCommon18GeofencingObserverP20onUserConsentChanged02isG5GivenySb_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingObserver>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingObserver>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "s:12MapboxCommon18GeofencingObserverP", "mangledName": "$s12MapboxCommon18GeofencingObserverP", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingOptions", "printedName": "GeofencingOptions", "children": [{"kind": "Var", "name": "maximumMonitoredFeatures", "printedName": "maximumMonitoredFeatures", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}], "declKind": "Var", "usr": "s:12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32Vvp", "mangledName": "$s12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32Vvp", "moduleName": "MapboxCommon", "declAttributes": ["HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}], "declKind": "Accessor", "usr": "s:12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32Vvg", "mangledName": "$s12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32Vvg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}], "declKind": "Accessor", "usr": "s:12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32Vvs", "mangledName": "$s12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32Vvs", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32VvM", "mangledName": "$s12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturess6UInt32VvM", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "_modify"}]}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(maximumMonitoredFeatures:)", "children": [{"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "hasDefaultArg": true, "usr": "s:s6UInt32V"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturesACs6UInt32V_tcfc", "mangledName": "$s12MapboxCommon17GeofencingOptionsV24maximumMonitoredFeaturesACs6UInt32V_tcfc", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl"], "spi_group_names": ["Experimental"], "init_kind": "Designated"}, {"kind": "Var", "name": "hashValue", "printedName": "hashValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:12MapboxCommon17GeofencingOptionsV9hashValueSivp", "mangledName": "$s12MapboxCommon17GeofencingOptionsV9hashValueSivp", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:12MapboxCommon17GeofencingOptionsV9hashValueSivg", "mangledName": "$s12MapboxCommon17GeofencingOptionsV9hashValueSivg", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}]}, {"kind": "Function", "name": "hash", "printedName": "hash(into:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "paramValueOwnership": "InOut", "usr": "s:s6HasherV"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingOptionsV4hash4intoys6HasherVz_tF", "mangledName": "$s12MapboxCommon17GeofencingOptionsV4hash4intoys6HasherVz_tF", "moduleName": "MapboxCommon", "implicit": true, "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "==", "printedName": "==(_:_:)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingOptionsV2eeoiySbAC_ACtFZ", "mangledName": "$s12MapboxCommon17GeofencingOptionsV2eeoiySbAC_ACtFZ", "moduleName": "MapboxCommon", "static": true, "implicit": true, "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingOptionsV10MarshallerV6toObjcyxAClFZ", "mangledName": "$s12MapboxCommon17GeofencingOptionsV10MarshallerV6toObjcyxAClFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingOptionsV10MarshallerV7toSwiftyACypFZ", "mangledName": "$s12MapboxCommon17GeofencingOptionsV10MarshallerV7toSwiftyACypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:12MapboxCommon17GeofencingOptionsV10MarshallerV", "mangledName": "$s12MapboxCommon17GeofencingOptionsV10MarshallerV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "isFromExtension": true, "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}], "declKind": "Struct", "usr": "s:12MapboxCommon17GeofencingOptionsV", "mangledName": "$s12MapboxCommon17GeofencingOptionsV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingPropertiesKeys", "printedName": "GeofencingPropertiesKeys", "children": [{"kind": "Var", "name": "dwellTimeKey", "printedName": "dwellTimeKey", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:12MapboxCommon24GeofencingPropertiesKeysO12dwellTimeKeySSvpZ", "mangledName": "$s12MapboxCommon24GeofencingPropertiesKeysO12dwellTimeKeySSvpZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["HasInitialValue", "HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:12MapboxCommon24GeofencingPropertiesKeysO12dwellTimeKeySSvgZ", "mangledName": "$s12MapboxCommon24GeofencingPropertiesKeysO12dwellTimeKeySSvgZ", "moduleName": "MapboxCommon", "static": true, "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}]}, {"kind": "Var", "name": "pointRadiusKey", "printedName": "pointRadiusKey", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:12MapboxCommon24GeofencingPropertiesKeysO14pointRadiusKeySSvpZ", "mangledName": "$s12MapboxCommon24GeofencingPropertiesKeysO14pointRadiusKeySSvpZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["HasInitialValue", "HasStorage", "AccessControl", "Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "isLet": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:12MapboxCommon24GeofencingPropertiesKeysO14pointRadiusKeySSvgZ", "mangledName": "$s12MapboxCommon24GeofencingPropertiesKeysO14pointRadiusKeySSvgZ", "moduleName": "MapboxCommon", "static": true, "implicit": true, "spi_group_names": ["Experimental"], "accessorKind": "get"}]}], "declKind": "Enum", "usr": "s:12MapboxCommon24GeofencingPropertiesKeysO", "mangledName": "$s12MapboxCommon24GeofencingPropertiesKeysO", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["SPIAccessControl"]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingService", "printedName": "GeofencingService", "children": [{"kind": "Function", "name": "configure", "printedName": "configure(options:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<(), MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP9configure7options8callbackyAA0C7OptionsV_ys6ResultOyytAA0C5ErrorVGctF", "mangledName": "$s12MapboxCommon17GeofencingServiceP9configure7options8callbackyAA0C7OptionsV_ys6ResultOyytAA0C5ErrorVGctF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getOptions", "printedName": "getOptions(callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<MapboxCommon.GeofencingOptions, MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.GeofencingOptions, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "GeofencingOptions", "printedName": "MapboxCommon.GeofencingOptions", "usr": "s:12MapboxCommon17GeofencingOptionsV"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP10getOptions8callbackyys6ResultOyAA0cF0VAA0C5ErrorVGc_tF", "mangledName": "$s12MapboxCommon17GeofencingServiceP10getOptions8callbackyys6ResultOyAA0cF0VAA0C5ErrorVGc_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "addFeature", "printedName": "addFeature(feature:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Feature", "printedName": "Turf.Feature", "usr": "s:4Turf7FeatureV"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(<PERSON>.Result<Swift.String, MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<Swift.String, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP10addFeature7feature8callbacky4Turf0F0V_ys6ResultOySSAA0C5ErrorVGctF", "mangledName": "$s12MapboxCommon17GeofencingServiceP10addFeature7feature8callbacky4Turf0F0V_ys6ResultOySSAA0C5ErrorVGctF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getFeature", "printedName": "getFeature(identifier:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<MapboxCommon.GeofenceState, MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.GeofenceState, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "GeofenceState", "printedName": "MapboxCommon.GeofenceState", "usr": "s:12MapboxCommon13GeofenceStateV"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP10getFeature10identifier8callbackySS_ys6ResultOyAA13GeofenceStateVAA0C5ErrorVGctF", "mangledName": "$s12MapboxCommon17GeofencingServiceP10getFeature10identifier8callbackySS_ys6ResultOyAA13GeofenceStateVAA0C5ErrorVGctF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "removeFeature", "printedName": "removeFeature(identifier:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<(), MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP13removeFeature10identifier8callbackySS_ys6ResultOyytAA0C5ErrorVGctF", "mangledName": "$s12MapboxCommon17GeofencingServiceP13removeFeature10identifier8callbackySS_ys6ResultOyytAA0C5ErrorVGctF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "clearFeatures", "printedName": "clearFeatures(callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<Swift.UInt32, MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<Swift.UInt32, MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP13clearFeatures8callbackyys6ResultOys6UInt32VAA0C5ErrorVGc_tF", "mangledName": "$s12MapboxCommon17GeofencingServiceP13clearFeatures8callbackyys6ResultOys6UInt32VAA0C5ErrorVGc_tF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "addObserver", "printedName": "addObserver(observer:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingObserver", "printedName": "any MapboxCommon.GeofencingObserver", "usr": "s:12MapboxCommon18GeofencingObserverP"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<(), MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP11addObserver8observer8callbackyAA0cF0_p_ys6ResultOyytAA0C5ErrorVGctF", "mangledName": "$s12MapboxCommon17GeofencingServiceP11addObserver8observer8callbackyAA0cF0_p_ys6ResultOyytAA0C5ErrorVGctF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "removeObserver", "printedName": "removeObserver(observer:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingObserver", "printedName": "any MapboxCommon.GeofencingObserver", "usr": "s:12MapboxCommon18GeofencingObserverP"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<(), MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon17GeofencingServiceP14removeObserver8observer8callbackyAA0cF0_p_ys6ResultOyytAA0C5ErrorVGctF", "mangledName": "$s12MapboxCommon17GeofencingServiceP14removeObserver8observer8callbackyAA0cF0_p_ys6ResultOyytAA0C5ErrorVGctF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0 where τ_0_0 : MapboxCommon.GeofencingService>", "sugared_genericSig": "<Self where Self : MapboxCommon.GeofencingService>", "protocolReq": true, "declAttributes": ["Documentation", "RawDocComment"], "spi_group_names": ["Experimental"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "s:12MapboxCommon17GeofencingServiceP", "mangledName": "$s12MapboxCommon17GeofencingServiceP", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "conformances": [{"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly", "SPIAccessControl"]}, {"kind": "TypeDecl", "name": "GeofencingUtils", "printedName": "GeofencingUtils", "children": [{"kind": "Function", "name": "setUserConsent", "printedName": "setUserConsent(isConsentGiven:callback:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeFunc", "name": "Function", "printedName": "(Swift.Result<(), MapboxCommon.GeofencingError>) -> ()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.GeofencingError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "GeofencingError", "printedName": "MapboxCommon.GeofencingError", "usr": "s:12MapboxCommon15GeofencingErrorV"}], "usr": "s:s6ResultO"}]}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingUtilsC14setUserConsent02isG5Given8callbackySb_ys6ResultOyytAA0C5ErrorVGctFZ", "mangledName": "$s12MapboxCommon15GeofencingUtilsC14setUserConsent02isG5Given8callbackySb_ys6ResultOyytAA0C5ErrorVGctFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "SPIAccessControl", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental", "Internal"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "getUserConsent", "printedName": "getUserConsent()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingUtilsC14getUserConsentSbyFZ", "mangledName": "$s12MapboxCommon15GeofencingUtilsC14getUserConsentSbyFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "isActive", "printedName": "isActive()", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "declKind": "Func", "usr": "s:12MapboxCommon15GeofencingUtilsC8isActiveSbyFZ", "mangledName": "$s12MapboxCommon15GeofencingUtilsC8isActiveSbyFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "Documentation", "SPIAccessControl", "RawDocComment"], "spi_group_names": ["Experimental"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:12MapboxCommon15GeofencingUtilsC", "mangledName": "$s12MapboxCommon15GeofencingUtilsC", "moduleName": "MapboxCommon", "declAttributes": ["Final", "AccessControl", "SPIAccessControl"], "spi_group_names": ["Experimental"], "hasMissingDesignatedInitializers": true, "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}, {"kind": "Import", "name": "CoreLocation", "printedName": "CoreLocation", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly"]}, {"kind": "Import", "name": "MapboxCommon_Private", "printedName": "MapboxCommon_Private", "declKind": "Import", "moduleName": "MapboxCommon", "declAttributes": ["ImplementationOnly"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "MapboxCommon"}, {"kind": "TypeDecl", "name": "SettingsServiceError", "printedName": "SettingsServiceError", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(description:)", "children": [{"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:12MapboxCommon20SettingsServiceErrorV11descriptionACSS_tcfc", "mangledName": "$s12MapboxCommon20SettingsServiceErrorV11descriptionACSS_tcfc", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl"], "init_kind": "Designated"}, {"kind": "Function", "name": "==", "printedName": "==(_:_:)", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}], "declKind": "Func", "usr": "s:12MapboxCommon20SettingsServiceErrorV2eeoiySbAC_ACtFZ", "mangledName": "$s12MapboxCommon20SettingsServiceErrorV2eeoiySbAC_ACtFZ", "moduleName": "MapboxCommon", "static": true, "implicit": true, "funcSelfKind": "NonMutating"}, {"kind": "Var", "name": "errorDescription", "printedName": "errorDescription", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:12MapboxCommon20SettingsServiceErrorV16errorDescriptionSSSgvp", "mangledName": "$s12MapboxCommon20SettingsServiceErrorV16errorDescriptionSSSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:12MapboxCommon20SettingsServiceErrorV16errorDescriptionSSSgvg", "mangledName": "$s12MapboxCommon20SettingsServiceErrorV16errorDescriptionSSSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Struct", "usr": "s:12MapboxCommon20SettingsServiceErrorV", "mangledName": "$s12MapboxCommon20SettingsServiceErrorV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "Error", "printedName": "Error", "usr": "s:s5ErrorP", "mangledName": "$ss5ErrorP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "LocalizedError", "printedName": "LocalizedError", "usr": "s:10Foundation14LocalizedErrorP", "mangledName": "$s10Foundation14LocalizedErrorP"}]}, {"kind": "TypeDecl", "name": "MapboxOptions", "printedName": "MapboxOptions", "children": [{"kind": "Var", "name": "accessToken", "printedName": "accessToken", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Var", "usr": "s:So16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvpZ", "mangledName": "$sSo16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvpZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final", "AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:So16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvgZ", "mangledName": "$sSo16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvgZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Accessor", "usr": "s:So16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvsZ", "mangledName": "$sSo16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvsZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvMZ", "mangledName": "$sSo16MBXMapboxOptionsC12MapboxCommonE11accessTokenSSvMZ", "moduleName": "MapboxCommon", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}], "declKind": "Class", "usr": "c:objc(cs)MBXMapboxOptions", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXMapboxOptions", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "NSExceptionHandler", "printedName": "NSExceptionHandler", "children": [{"kind": "Function", "name": "try", "printedName": "try(callback:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeFunc", "name": "Function", "printedName": "() throws -> τ_0_0", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "typeAttributes": ["noescape"]}], "declKind": "Func", "usr": "s:So19MBXExceptionHandlerC12MapboxCommonE3try8callbackxxyKXE_tKlFZ", "mangledName": "$sSo19MBXExceptionHandlerC12MapboxCommonE3try8callbackxxyKXE_tKlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["Final", "AccessControl", "DiscardableResult"], "isFromExtension": true, "throwing": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)MBXExceptionHandler", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXExceptionHandler", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "Location", "printedName": "Location", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(coordinate:timestamp:altitude:horizontalAccuracy:verticalAccuracy:speed:speedAccuracy:bearing:bearingAccuracy:floor:source:extra:)", "children": [{"kind": "TypeNominal", "name": "Location", "printedName": "MapboxCommon.Location", "usr": "c:objc(cs)MBXLocation"}, {"kind": "TypeNominal", "name": "CLLocationCoordinate2D", "printedName": "CoreLocation.CLLocationCoordinate2D", "usr": "c:@S@CLLocationCoordinate2D"}, {"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "hasDefaultArg": true, "usr": "s:10Foundation4DateV"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int?", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "hasDefaultArg": true, "usr": "s:Sq"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "Any?", "children": [{"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "hasDefaultArg": true, "usr": "s:Sq"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:So11MBXLocationC12MapboxCommonE10coordinate9timestamp8altitude18horizontalAccuracy08verticalH05speed0jH07bearing0kH05floor6source5extraABSo22CLLocationCoordinate2DV_10Foundation4DateVSdSgA6USiSgSSSgypSgtcfc", "mangledName": "$sSo11MBXLocationC12MapboxCommonE10coordinate9timestamp8altitude18horizontalAccuracy08verticalH05speed0jH07bearing0kH05floor6source5extraABSo22CLLocationCoordinate2DV_10Foundation4DateVSdSgA6USiSgSSSgypSgtcfc", "moduleName": "MapboxCommon", "declAttributes": ["Convenience", "AccessControl", "RawDocComment"], "isFromExtension": true, "init_kind": "Convenience"}, {"kind": "Var", "name": "coordinate", "printedName": "coordinate", "children": [{"kind": "TypeNominal", "name": "CLLocationCoordinate2D", "printedName": "CoreLocation.CLLocationCoordinate2D", "usr": "c:@S@CLLocationCoordinate2D"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE10coordinateSo22CLLocationCoordinate2DVvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE10coordinateSo22CLLocationCoordinate2DVvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CLLocationCoordinate2D", "printedName": "CoreLocation.CLLocationCoordinate2D", "usr": "c:@S@CLLocationCoordinate2D"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE10coordinateSo22CLLocationCoordinate2DVvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE10coordinateSo22CLLocationCoordinate2DVvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "timestamp", "printedName": "timestamp", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE9timestamp10Foundation4DateVvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE9timestamp10Foundation4DateVvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Date", "printedName": "Foundation.Date", "usr": "s:10Foundation4DateV"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE9timestamp10Foundation4DateVvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE9timestamp10Foundation4DateVvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "altitude", "printedName": "altitude", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE8altitudeSdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE8altitudeSdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE8altitudeSdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE8altitudeSdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "horizontalAccuracy", "printedName": "horizontalAccuracy", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE18horizontalAccuracySdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE18horizontalAccuracySdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE18horizontalAccuracySdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE18horizontalAccuracySdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "verticalAccuracy", "printedName": "verticalAccuracy", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE16verticalAccuracySdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE16verticalAccuracySdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE16verticalAccuracySdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE16verticalAccuracySdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "speed", "printedName": "speed", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE5speedSdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE5speedSdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE5speedSdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE5speedSdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "speedAccuracy", "printedName": "speedAccuracy", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE13speedAccuracySdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE13speedAccuracySdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE13speedAccuracySdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE13speedAccuracySdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "bearing", "printedName": "bearing", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE7bearingSdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE7bearingSdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE7bearingSdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE7bearingSdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "bearingAccuracy", "printedName": "bearingAccuracy", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE15bearingAccuracySdSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE15bearingAccuracySdSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Double?", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE15bearingAccuracySdSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE15bearingAccuracySdSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "floor", "printedName": "floor", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int?", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So11MBXLocationC12MapboxCommonE5floorSiSgvp", "mangledName": "$sSo11MBXLocationC12MapboxCommonE5floorSiSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int?", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So11MBXLocationC12MapboxCommonE5floorSiSgvg", "mangledName": "$sSo11MBXLocationC12MapboxCommonE5floorSiSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "TypeDecl", "name": "<PERSON><PERSON>", "printedName": "<PERSON><PERSON>", "children": [{"kind": "Function", "name": "toObjc", "printedName": "toObjc(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "Location", "printedName": "MapboxCommon.Location", "usr": "c:objc(cs)MBXLocation"}], "declKind": "Func", "usr": "s:So11MBXLocationC12MapboxCommonE10MarshallerV6toObjcyxABlFZ", "mangledName": "$sSo11MBXLocationC12MapboxCommonE10MarshallerV6toObjcyxABlFZ", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "toSwift", "printedName": "toSwift(_:)", "children": [{"kind": "TypeNominal", "name": "Location", "printedName": "MapboxCommon.Location", "usr": "c:objc(cs)MBXLocation"}, {"kind": "TypeNominal", "name": "ProtocolComposition", "printedName": "Any"}], "declKind": "Func", "usr": "s:So11MBXLocationC12MapboxCommonE10MarshallerV7toSwiftyABypFZ", "mangledName": "$sSo11MBXLocationC12MapboxCommonE10MarshallerV7toSwiftyABypFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["AccessControl"], "spi_group_names": ["Marshalling"], "funcSelfKind": "NonMutating"}], "declKind": "Struct", "usr": "s:So11MBXLocationC12MapboxCommonE10MarshallerV", "mangledName": "$sSo11MBXLocationC12MapboxCommonE10MarshallerV", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "SPIAccessControl"], "isFromExtension": true, "spi_group_names": ["Marshalling"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}]}], "declKind": "Class", "usr": "c:objc(cs)MBXLocation", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXLocation", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "HttpRequestError", "printedName": "HttpRequestError", "children": [{"kind": "Var", "name": "errorDescription", "printedName": "errorDescription", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So19MBXHttpRequestErrorC12MapboxCommonE16errorDescriptionSSSgvp", "mangledName": "$sSo19MBXHttpRequestErrorC12MapboxCommonE16errorDescriptionSSSgvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.String?", "children": [{"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So19MBXHttpRequestErrorC12MapboxCommonE16errorDescriptionSSSgvg", "mangledName": "$sSo19MBXHttpRequestErrorC12MapboxCommonE16errorDescriptionSSSgvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)MBXHttpRequestError", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXHttpRequestError", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}, {"kind": "Conformance", "name": "LocalizedError", "printedName": "LocalizedError", "usr": "s:10Foundation14LocalizedErrorP", "mangledName": "$s10Foundation14LocalizedErrorP"}, {"kind": "Conformance", "name": "Error", "printedName": "Error", "usr": "s:s5ErrorP", "mangledName": "$ss5ErrorP"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}]}, {"kind": "TypeDecl", "name": "HttpResponse", "printedName": "HttpResponse", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(identifier:request:result:)", "children": [{"kind": "TypeNominal", "name": "HttpResponse", "printedName": "MapboxCommon.HttpResponse", "usr": "c:objc(cs)MBXHttpResponse"}, {"kind": "TypeNominal", "name": "UInt64", "printedName": "Swift.UInt64", "usr": "s:s6UInt64V"}, {"kind": "TypeNominal", "name": "HttpRequest", "printedName": "MapboxCommon.HttpRequest", "usr": "c:objc(cs)MBXHttpRequest"}, {"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.HttpResponseData, MapboxCommon.HttpRequestError>", "children": [{"kind": "TypeNominal", "name": "HttpResponseData", "printedName": "MapboxCommon.HttpResponseData", "usr": "c:objc(cs)MBXHttpResponseData"}, {"kind": "TypeNominal", "name": "HttpRequestError", "printedName": "MapboxCommon.HttpRequestError", "usr": "c:objc(cs)MBXHttpRequestError"}], "usr": "s:s6ResultO"}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:So15MBXHttpResponseC12MapboxCommonE10identifier7request6resultABs6UInt64V_So0A7RequestCs6ResultOySo0aB4DataCSo0aI5ErrorCGtcfc", "mangledName": "$sSo15MBXHttpResponseC12MapboxCommonE10identifier7request6resultABs6UInt64V_So0A7RequestCs6ResultOySo0aB4DataCSo0aI5ErrorCGtcfc", "moduleName": "MapboxCommon", "declAttributes": ["Convenience", "AccessControl", "RawDocComment"], "isFromExtension": true, "init_kind": "Convenience"}, {"kind": "Var", "name": "result", "printedName": "result", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.HttpResponseData, MapboxCommon.HttpRequestError>", "children": [{"kind": "TypeNominal", "name": "HttpResponseData", "printedName": "MapboxCommon.HttpResponseData", "usr": "c:objc(cs)MBXHttpResponseData"}, {"kind": "TypeNominal", "name": "HttpRequestError", "printedName": "MapboxCommon.HttpRequestError", "usr": "c:objc(cs)MBXHttpRequestError"}], "usr": "s:s6ResultO"}], "declKind": "Var", "usr": "s:So15MBXHttpResponseC12MapboxCommonE6results6ResultOySo0aB4DataCSo0A12RequestErrorCGvp", "mangledName": "$sSo15MBXHttpResponseC12MapboxCommonE6results6ResultOySo0aB4DataCSo0A12RequestErrorCGvp", "moduleName": "MapboxCommon", "declAttributes": ["AccessControl", "RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<MapboxCommon.HttpResponseData, MapboxCommon.HttpRequestError>", "children": [{"kind": "TypeNominal", "name": "HttpResponseData", "printedName": "MapboxCommon.HttpResponseData", "usr": "c:objc(cs)MBXHttpResponseData"}, {"kind": "TypeNominal", "name": "HttpRequestError", "printedName": "MapboxCommon.HttpRequestError", "usr": "c:objc(cs)MBXHttpRequestError"}], "usr": "s:s6ResultO"}], "declKind": "Accessor", "usr": "s:So15MBXHttpResponseC12MapboxCommonE6results6ResultOySo0aB4DataCSo0A12RequestErrorCGvg", "mangledName": "$sSo15MBXHttpResponseC12MapboxCommonE6results6ResultOySo0aB4DataCSo0A12RequestErrorCGvg", "moduleName": "MapboxCommon", "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)MBXHttpResponse", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXHttpResponse", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "SettingsService", "printedName": "SettingsService", "children": [{"kind": "Function", "name": "set", "printedName": "set(key:value:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.SettingsServiceError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "declKind": "Func", "usr": "s:So18MBXSettingsServiceC12MapboxCommonE3set3key5values6ResultOyytAC08SettingsB5ErrorVGSS_xtlF", "mangledName": "$sSo18MBXSettingsServiceC12MapboxCommonE3set3key5values6ResultOyytAC08SettingsB5ErrorVGSS_xtlF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "get", "printedName": "get(key:type:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<τ_0_0, MapboxCommon.SettingsServiceError>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "τ_0_0.Type", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}], "declKind": "Func", "usr": "s:So18MBXSettingsServiceC12MapboxCommonE3get3key4types6ResultOyxAC08SettingsB5ErrorVGSS_xmtlF", "mangledName": "$sSo18MBXSettingsServiceC12MapboxCommonE3get3key4types6ResultOyxAC08SettingsB5ErrorVGSS_xmtlF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "get", "printedName": "get(key:type:defaultValue:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<τ_0_0, MapboxCommon.SettingsServiceError>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "τ_0_0.Type", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}]}, {"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "declKind": "Func", "usr": "s:So18MBXSettingsServiceC12MapboxCommonE3get3key4type12defaultValues6ResultOyxAC08SettingsB5ErrorVGSS_xmxtlF", "mangledName": "$sSo18MBXSettingsServiceC12MapboxCommonE3get3key4type12defaultValues6ResultOyxAC08SettingsB5ErrorVGSS_xmxtlF", "moduleName": "MapboxCommon", "genericSig": "<τ_0_0>", "sugared_genericSig": "<T>", "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "erase", "printedName": "erase(key:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<(), MapboxCommon.SettingsServiceError>", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "s:So18MBXSettingsServiceC12MapboxCommonE5erase3keys6ResultOyytAC08SettingsB5ErrorVGSS_tF", "mangledName": "$sSo18MBXSettingsServiceC12MapboxCommonE5erase3keys6ResultOyytAC08SettingsB5ErrorVGSS_tF", "moduleName": "MapboxCommon", "isFromExtension": true, "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "has", "printedName": "has(key:)", "children": [{"kind": "TypeNominal", "name": "Result", "printedName": "Swift.Result<<PERSON><PERSON>, MapboxCommon.SettingsServiceError>", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}, {"kind": "TypeNominal", "name": "SettingsServiceError", "printedName": "MapboxCommon.SettingsServiceError", "usr": "s:12MapboxCommon20SettingsServiceErrorV"}], "usr": "s:s6ResultO"}, {"kind": "TypeNominal", "name": "String", "printedName": "Swift.String", "usr": "s:SS"}], "declKind": "Func", "usr": "s:So18MBXSettingsServiceC12MapboxCommonE3has3keys6ResultOySbAC08SettingsB5ErrorVGSS_tF", "mangledName": "$sSo18MBXSettingsServiceC12MapboxCommonE3has3keys6ResultOySbAC08SettingsB5ErrorVGSS_tF", "moduleName": "MapboxCommon", "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)MBXSettingsService", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXSettingsService", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "SettingsServiceFactory", "printedName": "SettingsServiceFactory", "children": [{"kind": "Function", "name": "getInstance", "printedName": "getInstance(storageType:)", "children": [{"kind": "TypeNominal", "name": "SettingsService", "printedName": "MapboxCommon.SettingsService", "usr": "c:objc(cs)MBXSettingsService"}, {"kind": "TypeNominal", "name": "SettingsServiceStorageType", "printedName": "MapboxCommon.SettingsServiceStorageType", "usr": "c:@E@MBXSettingsServiceStorageType"}], "declKind": "Func", "usr": "s:So25MBXSettingsServiceFactoryC12MapboxCommonE11getInstance11storageTypeSo0aB0CSo0ab7StorageI0V_tFZ", "mangledName": "$sSo25MBXSettingsServiceFactoryC12MapboxCommonE11getInstance11storageTypeSo0aB0CSo0ab7StorageI0V_tFZ", "moduleName": "MapboxCommon", "static": true, "declAttributes": ["Final"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)MBXSettingsServiceFactory", "moduleName": "MapboxCommon", "isOpen": true, "objc_name": "MBXSettingsServiceFactory", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Copyable", "printedName": "Copyable", "usr": "s:s8CopyableP", "mangledName": "$ss8CopyableP"}, {"kind": "Conformance", "name": "Escapable", "printedName": "Escapable", "usr": "s:s9EscapableP", "mangledName": "$ss9EscapableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 371, "length": 12, "value": "1"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 471, "length": 21, "value": "2"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 612, "length": 29, "value": "3"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 737, "length": 26, "value": "4"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 868, "length": 27, "value": "5"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 983, "length": 25, "value": "6"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1118, "length": 27, "value": "7"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1244, "length": 19, "value": "8"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1364, "length": 15, "value": "9"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1468, "length": 14, "value": "10"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1590, "length": 20, "value": "11"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1711, "length": 16, "value": "12"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingErrorType.swift", "kind": "IntegerLiteral", "offset": 1849, "length": 16, "value": "13"}, {"filePath": "/Users/<USER>/project/actions-runner/_work/mapbox-sdk/mapbox-sdk/projects/common/components/geofencing/generated/swift_compat/GeofencingOptions.swift", "kind": "IntegerLiteral", "offset": 483, "length": 6, "value": "100000"}]}