{"source": "/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/ExploreActivitiesView.swift", "tables": {"Localizable": [{"comment": "", "key": "探索路线", "location": {"startingColumn": 22, "startingLine": 170}}, {"comment": "", "key": "当前位置: %@", "location": {"startingColumn": 26, "startingLine": 207}}, {"comment": "", "key": "AI定制探索路线", "location": {"startingColumn": 22, "startingLine": 229}}, {"comment": "", "key": "告诉我您的需求，让AI为您生成专属骑行路线", "location": {"startingColumn": 22, "startingLine": 233}}, {"comment": "", "key": "起始位置", "location": {"startingColumn": 26, "startingLine": 245}}, {"comment": "", "key": "路线需求", "location": {"startingColumn": 26, "startingLine": 293}}, {"comment": "", "key": "例如：想骑15公里，途经公园和咖啡店，难度简单", "location": {"startingColumn": 34, "startingLine": 316}}, {"comment": "", "key": "行程距离", "location": {"startingColumn": 26, "startingLine": 329}}, {"comment": "", "key": "最短距离", "location": {"startingColumn": 34, "startingLine": 335}}, {"comment": "", "key": "%lld公里", "location": {"startingColumn": 34, "startingLine": 341}}, {"comment": "", "key": "最远距离", "location": {"startingColumn": 34, "startingLine": 355}}, {"comment": "", "key": "%lld公里", "location": {"startingColumn": 34, "startingLine": 361}}, {"comment": "", "key": "路线距离: %lld-%lld公里", "location": {"startingColumn": 34, "startingLine": 376}}, {"comment": "", "key": "正在生成路线...", "location": {"startingColumn": 51, "startingLine": 418}}, {"comment": "", "key": "智能生成路线", "location": {"startingColumn": 77, "startingLine": 418}}, {"comment": "", "key": "正在生成您的专属路线", "location": {"startingColumn": 26, "startingLine": 466}}, {"comment": "", "key": "请稍候，AI正在为您分析并规划最佳骑行体验", "location": {"startingColumn": 26, "startingLine": 471}}, {"comment": "", "key": "%lld%%", "location": {"startingColumn": 34, "startingLine": 502}}, {"comment": "", "key": "重新设置", "location": {"startingColumn": 20, "startingLine": 556}}, {"comment": "", "key": "暂无路线", "location": {"startingColumn": 22, "startingLine": 547}}, {"comment": "", "key": "请重新生成路线", "location": {"startingColumn": 22, "startingLine": 551}}, {"comment": "", "key": "重新生成", "location": {"startingColumn": 32, "startingLine": 605}}, {"comment": "", "key": "🎯 基于您的需求定制", "location": {"startingColumn": 34, "startingLine": 587}}, {"comment": "", "key": "起始位置: %@", "location": {"startingColumn": 34, "startingLine": 591}}, {"comment": "", "key": "需求: %@", "location": {"startingColumn": 38, "startingLine": 596}}, {"comment": "", "key": "生成路线图", "location": {"startingColumn": 34, "startingLine": 1342}}, {"comment": "", "key": "起点", "location": {"startingColumn": 38, "startingLine": 1359}}, {"comment": "", "key": "终点", "location": {"startingColumn": 38, "startingLine": 1374}}, {"comment": "", "key": "%@ km", "location": {"startingColumn": 34, "startingLine": 1394}}, {"comment": "", "key": "%lld 个点位", "location": {"startingColumn": 38, "startingLine": 1446}}, {"comment": "", "key": "路线详情", "location": {"startingColumn": 30, "startingLine": 1548}}, {"comment": "", "key": "距离", "location": {"startingColumn": 38, "startingLine": 1492}}, {"comment": "", "key": "%@ km", "location": {"startingColumn": 38, "startingLine": 1496}}, {"comment": "", "key": "难度", "location": {"startingColumn": 38, "startingLine": 1502}}, {"comment": "", "key": "开始骑行", "location": {"startingColumn": 34, "startingLine": 1523}}, {"comment": "", "key": "关闭", "location": {"startingColumn": 28, "startingLine": 1552}}]}, "version": 1}