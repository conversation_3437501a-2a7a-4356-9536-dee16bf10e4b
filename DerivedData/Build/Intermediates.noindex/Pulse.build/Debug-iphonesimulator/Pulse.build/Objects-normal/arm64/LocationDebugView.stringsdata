{"source": "/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/LocationDebugView.swift", "tables": {"Localizable": [{"comment": "", "key": "定位调试信息", "location": {"startingColumn": 18, "startingLine": 10}}, {"comment": "", "key": "位置: 未获取", "location": {"startingColumn": 26, "startingLine": 46}}, {"comment": "", "key": "天气: 未获取", "location": {"startingColumn": 26, "startingLine": 58}}, {"comment": "", "key": "错误: %@", "location": {"startingColumn": 26, "startingLine": 63}}, {"comment": "", "key": "定位服务:", "location": {"startingColumn": 26, "startingLine": 16}}, {"comment": "", "key": "已启用", "location": {"startingColumn": 62, "startingLine": 18}}, {"comment": "", "key": "未启用", "location": {"startingColumn": 76, "startingLine": 18}}, {"comment": "", "key": "权限状态:", "location": {"startingColumn": 26, "startingLine": 23}}, {"comment": "", "key": "当前位置:", "location": {"startingColumn": 30, "startingLine": 31}}, {"comment": "", "key": "纬度: %@", "location": {"startingColumn": 30, "startingLine": 32}}, {"comment": "", "key": "经度: %@", "location": {"startingColumn": 30, "startingLine": 35}}, {"comment": "", "key": "精度: %@m", "location": {"startingColumn": 30, "startingLine": 38}}, {"comment": "", "key": "时间: %@", "location": {"startingColumn": 30, "startingLine": 41}}, {"comment": "", "key": "天气城市:", "location": {"startingColumn": 30, "startingLine": 52}}, {"comment": "", "key": "请求定位权限", "location": {"startingColumn": 24, "startingLine": 72}}, {"comment": "", "key": "获取当前位置", "location": {"startingColumn": 24, "startingLine": 79}}, {"comment": "", "key": "刷新天气", "location": {"startingColumn": 24, "startingLine": 86}}, {"comment": "", "key": "定位调试信息", "location": {"startingColumn": 18, "startingLine": 172}}, {"comment": "", "key": "位置: 未获取", "location": {"startingColumn": 26, "startingLine": 208}}, {"comment": "", "key": "天气: 未获取", "location": {"startingColumn": 26, "startingLine": 220}}, {"comment": "", "key": "错误: %@", "location": {"startingColumn": 26, "startingLine": 225}}, {"comment": "", "key": "定位服务:", "location": {"startingColumn": 26, "startingLine": 178}}, {"comment": "", "key": "已启用", "location": {"startingColumn": 62, "startingLine": 180}}, {"comment": "", "key": "未启用", "location": {"startingColumn": 76, "startingLine": 180}}, {"comment": "", "key": "权限状态:", "location": {"startingColumn": 26, "startingLine": 185}}, {"comment": "", "key": "使用时允许", "location": {"startingColumn": 26, "startingLine": 187}}, {"comment": "", "key": "当前位置:", "location": {"startingColumn": 30, "startingLine": 193}}, {"comment": "", "key": "纬度: %@", "location": {"startingColumn": 30, "startingLine": 194}}, {"comment": "", "key": "经度: %@", "location": {"startingColumn": 30, "startingLine": 197}}, {"comment": "", "key": "精度: %@m", "location": {"startingColumn": 30, "startingLine": 200}}, {"comment": "", "key": "时间: %@", "location": {"startingColumn": 30, "startingLine": 203}}, {"comment": "", "key": "天气城市:", "location": {"startingColumn": 30, "startingLine": 214}}, {"comment": "", "key": "请求定位权限", "location": {"startingColumn": 24, "startingLine": 234}}, {"comment": "", "key": "获取当前位置", "location": {"startingColumn": 24, "startingLine": 240}}, {"comment": "", "key": "刷新天气", "location": {"startingColumn": 24, "startingLine": 245}}, {"comment": "", "key": "定位调试信息", "location": {"startingColumn": 18, "startingLine": 260}}, {"comment": "", "key": "定位服务:", "location": {"startingColumn": 26, "startingLine": 266}}, {"comment": "", "key": "已启用", "location": {"startingColumn": 26, "startingLine": 268}}, {"comment": "", "key": "权限状态:", "location": {"startingColumn": 26, "startingLine": 273}}, {"comment": "", "key": "使用时允许", "location": {"startingColumn": 26, "startingLine": 275}}, {"comment": "", "key": "当前位置:", "location": {"startingColumn": 26, "startingLine": 280}}, {"comment": "", "key": "纬度: 39.904200", "location": {"startingColumn": 26, "startingLine": 281}}, {"comment": "", "key": "经度: 116.407400", "location": {"startingColumn": 26, "startingLine": 284}}, {"comment": "", "key": "精度: 5.0m", "location": {"startingColumn": 26, "startingLine": 287}}, {"comment": "", "key": "天气城市:", "location": {"startingColumn": 26, "startingLine": 293}}, {"comment": "", "key": "北京", "location": {"startingColumn": 26, "startingLine": 295}}, {"comment": "", "key": "请求定位权限", "location": {"startingColumn": 24, "startingLine": 303}}, {"comment": "", "key": "获取当前位置", "location": {"startingColumn": 24, "startingLine": 309}}, {"comment": "", "key": "刷新天气", "location": {"startingColumn": 24, "startingLine": 314}}]}, "version": 1}