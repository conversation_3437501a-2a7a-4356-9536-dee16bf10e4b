{"source": "/Users/<USER>/workspace/myApps/Pulse/Pulse/CloudKitMinimal.swift", "tables": {"Localizable": [{"comment": "", "key": "CloudKit 开发测试", "location": {"startingColumn": 30, "startingLine": 471}}, {"comment": "", "key": "环境设置", "location": {"startingColumn": 25, "startingLine": 381}}, {"comment": "", "key": "CloudKit 状态", "location": {"startingColumn": 25, "startingLine": 399}}, {"comment": "", "key": "Schema 管理", "location": {"startingColumn": 25, "startingLine": 413}}, {"comment": "", "key": "Development 模式说明", "location": {"startingColumn": 29, "startingLine": 443}}, {"comment": "", "key": "执行日志", "location": {"startingColumn": 29, "startingLine": 462}}, {"comment": "", "key": "使用 Development 环境", "location": {"startingColumn": 28, "startingLine": 382}}, {"comment": "", "key": "Development: 可以直接创建 Schema", "location": {"startingColumn": 26, "startingLine": 393}}, {"comment": "", "key": "Production: 需要 Dashboard 手动创建", "location": {"startingColumn": 26, "startingLine": 394}}, {"comment": "", "key": "当前环境", "location": {"startingColumn": 30, "startingLine": 386}}, {"comment": "", "key": "Development", "location": {"startingColumn": 67, "startingLine": 388}}, {"comment": "", "key": "Production", "location": {"startingColumn": 83, "startingLine": 388}}, {"comment": "", "key": "检查连接", "location": {"startingColumn": 28, "startingLine": 407}}, {"comment": "", "key": "状态", "location": {"startingColumn": 30, "startingLine": 401}}, {"comment": "", "key": "Production 环境: 请在 CloudKit Dashboard 手动创建", "location": {"startingColumn": 30, "startingLine": 436}}, {"comment": "", "key": "检查 UserProfile 是否存在", "location": {"startingColumn": 28, "startingLine": 414}}, {"comment": "", "key": "🔍 扫描所有记录类型", "location": {"startingColumn": 28, "startingLine": 419}}, {"comment": "", "key": "🎯 直接测试 UserProfile", "location": {"startingColumn": 28, "startingLine": 424}}, {"comment": "", "key": "创建 UserProfile Schema", "location": {"startingColumn": 28, "startingLine": 429}}, {"comment": "", "key": "✅ 可以直接创建 Schema", "location": {"startingColumn": 34, "startingLine": 445}}, {"comment": "", "key": "✅ 立即测试新功能", "location": {"startingColumn": 34, "startingLine": 448}}, {"comment": "", "key": "⚠️ 使用私有数据库测试", "location": {"startingColumn": 34, "startingLine": 451}}, {"comment": "", "key": "🚀 测试成功后部署到 Production", "location": {"startingColumn": 34, "startingLine": 454}}]}, "version": 1}