{"source": "/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/SettingsView.swift", "tables": {"Localizable": [{"comment": "", "key": "%lld 次骑行", "location": {"startingColumn": 34, "startingLine": 336}}, {"comment": "", "key": "%@ %@", "location": {"startingColumn": 42, "startingLine": 412}}, {"comment": "", "key": "%@ %@", "location": {"startingColumn": 34, "startingLine": 423}}, {"comment": "", "key": "", "location": {"startingColumn": 28, "startingLine": 460}}, {"comment": "", "key": "", "location": {"startingColumn": 28, "startingLine": 532}}, {"comment": "", "key": "", "location": {"startingColumn": 28, "startingLine": 543}}, {"comment": "", "key": "关于", "location": {"startingColumn": 30, "startingLine": 1028}}, {"comment": "", "key": "Pulse", "location": {"startingColumn": 34, "startingLine": 956}}, {"comment": "", "key": "版本 1.0.0", "location": {"startingColumn": 34, "startingLine": 960}}, {"comment": "", "key": "关于应用", "location": {"startingColumn": 30, "startingLine": 969}}, {"comment": "", "key": "Pulse 是一款专业的骑行记录应用，帮助您记录每一次骑行旅程，追踪运动数据，享受健康生活。", "location": {"startingColumn": 30, "startingLine": 973}}, {"comment": "", "key": "主要功能", "location": {"startingColumn": 30, "startingLine": 983}}, {"comment": "", "key": "开发者", "location": {"startingColumn": 30, "startingLine": 1015}}, {"comment": "", "key": "© 2024 Pulse Team", "location": {"startingColumn": 30, "startingLine": 1019}}, {"comment": "", "key": "完成", "location": {"startingColumn": 28, "startingLine": 1032}}, {"comment": "", "key": "帮助与支持", "location": {"startingColumn": 30, "startingLine": 1184}}, {"comment": "", "key": "如果您在使用过程中遇到任何问题，请通过以下方式联系我们，我们将竭诚为您服务。", "location": {"startingColumn": 26, "startingLine": 1109}}, {"comment": "", "key": "帮助与支持", "location": {"startingColumn": 30, "startingLine": 1102}}, {"comment": "", "key": "常见问题", "location": {"startingColumn": 34, "startingLine": 1153}}, {"comment": "", "key": "完成", "location": {"startingColumn": 28, "startingLine": 1188}}, {"comment": "", "key": "数据导出", "location": {"startingColumn": 30, "startingLine": 1474}}, {"comment": "", "key": "数据导出", "location": {"startingColumn": 34, "startingLine": 1368}}, {"comment": "", "key": "选择导出格式和包含的数据类型", "location": {"startingColumn": 34, "startingLine": 1372}}, {"comment": "", "key": "导出格式", "location": {"startingColumn": 34, "startingLine": 1383}}, {"comment": "", "key": "包含数据", "location": {"startingColumn": 34, "startingLine": 1402}}, {"comment": "", "key": "导出中...", "location": {"startingColumn": 52, "startingLine": 1439}}, {"comment": "", "key": "开始导出", "location": {"startingColumn": 69, "startingLine": 1439}}, {"comment": "", "key": "取消", "location": {"startingColumn": 28, "startingLine": 1478}}, {"comment": "", "key": "", "location": {"startingColumn": 20, "startingLine": 1601}}]}, "version": 1}