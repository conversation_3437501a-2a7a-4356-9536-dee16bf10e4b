/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/WeatherManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/TaskDetailPanel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/LiveActivityManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/UserAvatarView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/DeepSeekAPIManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/AchievementDetailView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/LocationPickerView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/CloudBaseAuthView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/LocationDebugView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/StatsView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/CloudKitMinimal.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/ActivityManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/RouteManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/PhotoGalleryPanel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/WeatherCardView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/VoiceAnnouncementManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/ActivityMapView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/SimpleMapboxLocationPicker.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/SettingsView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/WeatherAchievementManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/HistoryTrackManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/HistoryView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/RideView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/ExploreActivitiesView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/RideSessionManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/RideSummaryView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Models/AchievementModel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/ActivityDetailView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/AppleMapDirectionsService.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/PulseApp.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/ActivitySubscriptionManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/RideEndConfirmationPanel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/LocalizationManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/CameraManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/RideRecordManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/PermissionManagementView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Utils/WeatherConfig.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/LocationManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/CloudBaseManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/MapSelectionView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Models/TaskModel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/ContentView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Models/AchievementDataProvider.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/ActiveTaskCard.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Models/RideModel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/CreateActivityView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/MapboxLocationTestView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/MapboxLocationManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/WidgetDataManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Utils/RideSnapshotManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/AchievementNotificationView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/ActivityCloudKitManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/WelcomeSignInView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/AchievementView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/MapboxMapView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Utils/MapboxAvailability.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/CloudKitUserManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/UserProfileView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/AchievementRowView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/HistoryTracksPanel.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/UserProfileManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Models/ExploreActivity.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/ActivityView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/UserProfileEditView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/Components/LoginRequiredView.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/ExploreManager.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Persistence.swift
/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/MapboxDirectionsService.swift
/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/DerivedSources/CoreDataGenerated/Pulse/Item+CoreDataClass.swift
/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/DerivedSources/CoreDataGenerated/Pulse/Item+CoreDataProperties.swift
/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/DerivedSources/CoreDataGenerated/Pulse/Pulse+CoreDataModel.swift
/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/DerivedSources/GeneratedAssetSymbols.swift
