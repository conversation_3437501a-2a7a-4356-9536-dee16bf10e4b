"/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/CameraManager.swift":
  const-values: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/CameraManager.swiftconstvalues"
  object: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/CameraManager.o"
  dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/CameraManager.d"
  swift-dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/CameraManager.swiftdeps"
  diagnostics: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/CameraManager.dia"
"/Users/<USER>/workspace/myApps/Pulse/Pulse/Views/RideEndConfirmationPanel.swift":
  swift-dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideEndConfirmationPanel.swiftdeps"
  dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideEndConfirmationPanel.d"
  object: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideEndConfirmationPanel.o"
  const-values: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideEndConfirmationPanel.swiftconstvalues"
  diagnostics: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideEndConfirmationPanel.dia"
"/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/LocalizationManager.swift":
  swift-dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/LocalizationManager.swiftdeps"
  const-values: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/LocalizationManager.swiftconstvalues"
  object: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/LocalizationManager.o"
  dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/LocalizationManager.d"
  diagnostics: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/LocalizationManager.dia"
"/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/RideRecordManager.swift":
  diagnostics: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideRecordManager.dia"
  const-values: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideRecordManager.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideRecordManager.swiftdeps"
  dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideRecordManager.d"
  object: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/RideRecordManager.o"
"/Users/<USER>/workspace/myApps/Pulse/Pulse/Managers/ActivitySubscriptionManager.swift":
  diagnostics: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/ActivitySubscriptionManager.dia"
  dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/ActivitySubscriptionManager.d"
  object: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/ActivitySubscriptionManager.o"
  const-values: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/ActivitySubscriptionManager.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/Pulse.build/Debug-iphonesimulator/Pulse.build/Objects-normal/arm64/ActivitySubscriptionManager.swiftdeps"
