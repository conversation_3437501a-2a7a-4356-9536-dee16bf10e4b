(version 1)
(allow default)
(disable-callouts)
(import "/System/Library/Sandbox/Profiles/bsd.sb")

;; Only deny SRCROOT, PROJECT_DIR and build dirs
(deny file-read* file-write* (subpath (param "SRCROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "PROJECT_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "OBJROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "SYMROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "DSTROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "SHARED_PRECOMPS_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "CACHE_ROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "CONFIGURATION_BUILD_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "CONFIGURATION_TEMP_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "LOCROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "LOCSYMROOT")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "INDEX_PRECOMPS_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(deny file-read* file-write* (subpath (param "INDEX_DATA_STORE_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(allow file-read* file-write* (subpath (param "TEMP_DIR")))

(deny file-read* file-write* (subpath (param "DERIVED_FILE_DIR")) (with message "de0ab989435aee6fa82df34b04e48a32"))
(allow file-read* (literal (param "SRCROOT")))
(allow file-read* (literal (param "PROJECT_DIR")))
;; Allow file-read for all the parents of declared input/outputs
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_0")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_1")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_2")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_3")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_4")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_5")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_6")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_7")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_8")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_9")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_10")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_11")))
(allow file-read* (literal (param "SCRIPT_INPUT_ANCESTOR_12")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_0")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_1")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_2")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_3")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_4")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_5")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_6")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_7")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_8")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_9")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_10")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_11")))
(allow file-read* (literal (param "SCRIPT_OUTPUT_ANCESTOR_12")))
;; Allow file-read for resolved (flattened) inputs
(allow file-read* (subpath (param "SCRIPT_INPUT_FILE_0")))
(allow file-read* (subpath (param "SCRIPT_INPUT_FILE_1")))
(allow file-read* (literal (param "SCRIPT_INPUT_FILE_2")))

;; Allow read+write for declared and resolved (flattened) outputs
(allow file-read* file-write* (literal (param "SCRIPT_OUTPUT_FILE_0")))

