{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "672d8c4e5e4501786b9bd984bab4cfb36b3a5087a6d2344ceee063750cf51df7"}], "containerPath": "/Users/<USER>/workspace/myApps/Pulse/Pulse.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.5", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/workspace/myApps/Pulse/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.5", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "CLANG_COVERAGE_MAPPING": "YES", "CLANG_PROFILE_DATA_DIRECTORY": "/Users/<USER>/workspace/myApps/Pulse/DerivedData/Build/ProfileData", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "108", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "F63E1B4F-BD6B-4A51-8915-0BE1278BA91B", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.5", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}