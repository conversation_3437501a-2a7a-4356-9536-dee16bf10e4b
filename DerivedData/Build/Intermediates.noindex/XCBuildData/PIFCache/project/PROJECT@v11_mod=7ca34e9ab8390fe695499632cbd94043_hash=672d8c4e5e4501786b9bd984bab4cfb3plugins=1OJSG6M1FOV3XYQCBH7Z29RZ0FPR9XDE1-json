{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "R37WQ24K3U", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb340b3ec37b8c451051834f75b82e585eb", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "R37WQ24K3U", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SWIFT_COMPILATION_MODE": "wholemodule"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb3684de55234103ff2ef2167904d5a7b05", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.plist.entitlements", "guid": "672d8c4e5e4501786b9bd984bab4cfb37bb89eaafe0f6043ba232fc56a6d76ac", "path": "PulseWidgetExtension.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "672d8c4e5e4501786b9bd984bab4cfb3dfdbb6503fd66d5e82111124abf0a953", "path": "GoogleService-Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "net.daringfireball.markdown", "guid": "672d8c4e5e4501786b9bd984bab4cfb32f0aa35e9bab9bb933191ee81b0e9c6f", "path": "LICENSE.md", "sourceTree": "<group>", "type": "file"}, {"expectedSignature": "AppleDeveloperProgram:GJZR2MEM28:Mapbox, Inc.", "fileType": "wrapper.xcframework", "guid": "672d8c4e5e4501786b9bd984bab4cfb38cd781d45425ed88122387f65c67e7bf", "path": "MapboxCommon.xcframework", "sourceTree": "<group>", "type": "file"}, {"expectedSignature": "AppleDeveloperProgram:GJZR2MEM28:Mapbox, Inc.", "fileType": "wrapper.xcframework", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d35f32bcae81cb717b08a42a0f15ebea", "path": "MapboxCoreMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"expectedSignature": "AppleDeveloperProgram:GJZR2MEM28:Mapbox, Inc.", "fileType": "wrapper.xcframework", "guid": "672d8c4e5e4501786b9bd984bab4cfb388ec06513fa07201213cee51a9b92b7f", "path": "MapboxMaps.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "672d8c4e5e4501786b9bd984bab4cfb38c703de060951d1b6313450a2763b657", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"expectedSignature": "AppleDeveloperProgram:GJZR2MEM28:Mapbox, Inc.", "fileType": "wrapper.xcframework", "guid": "672d8c4e5e4501786b9bd984bab4cfb3746bcb433658af265a44b19ae37bc10a", "path": "Turf.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3fb557322a09032cbc58321aedaf5f3b1", "name": "artifacts", "path": "artifacts", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3c22f853e64a2e2392c76ba76514adec6", "path": "ActivityCloudKitManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a99084012685e980df87118536e66d2c", "path": "ActivityManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb37f4aee8775b842094dd06dc101709f19", "path": "ActivitySubscriptionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb31eb0d723f466be3b9d10bc90ba11b67a", "path": "AppleMapDirectionsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb369f8755335b5308e7a3f83644c1f0ec4", "path": "CameraManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3bcd23b0dbd078b5402292eaae47f32bc", "path": "DeepSeekAPIManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb30051070370057cad288c985254d146ca", "path": "ExploreManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ffc9660f50d54ccaa8089a503ac79b2a", "path": "HistoryTrackManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb32c3ff067b4e34efee47e86c3927a2bfe", "path": "LiveActivityManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a3637b8de3b0ef038f3a7e13c3698140", "path": "LocalizationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb355ecff637f9971d21bfebce753d3e11f", "path": "LocationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb36e797e43b17ff16e14982373825b2c4e", "path": "MapboxDirectionsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3633ab938dd5f643115342af79a7ca464", "path": "MapboxLocationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb34ad68ccf9cc52dc6630f8e3af3c4a908", "path": "RideRecordManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3b3b85ba9f081675988f6eb15f226da2c", "path": "RideSessionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb35e01c474e78729dd802579d5a2a1f385", "path": "RouteManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb34fde1aeddbc5c86ca8e0468282dbd6ee", "path": "UserProfileManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb38c8b47218905b001d209e2a4c282a161", "path": "VoiceAnnouncementManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e6315f8ddaed751f18bf90546de7b89d", "path": "WeatherAchievementManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3eddc3b1be1f7494f78c00ed34a1feaf2", "path": "WeatherManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d1718f095a79a53520e828a9a15f3181", "path": "WidgetDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3fd12d957a74644d7c1c8b6379ba0d53b", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb383dd8707d52bd5a104646cccd8a58cd7", "path": "AchievementDataProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d6f47ca6df4aaaf537e4ebea97e94478", "path": "AchievementModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e0387467942fa921b4ae02896cb20c82", "path": "ExploreActivity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ca8cb7b4b6b9be8c21e30f574ac96eb7", "path": "RideModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb31cff990fc3a9fe40b6c29f98309dcdef", "path": "TaskModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb33fa08a329aafd9fb125ff821842d3c27", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fd48261e9c770b4e0dbce28e55f858f5", "path": "MapboxAvailability.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3954503362c9daaad55d476594397856c", "path": "RideSnapshotManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3f1ef76465a039df9cfb1c960bc4c1239", "path": "WeatherConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3b2387f6ba4f6600f7d8c33b3f96a1dfe", "name": "Utils", "path": "Utils", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb310c8b92bc28a5c00a2c206da90a6c947", "path": "AchievementNotificationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb353901711a912427470d30be330b4e6b5", "path": "AchievementRowView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3111b87c93ec556e8391c93d4f0a88edb", "path": "LocationDebugView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb340420861e2d02812541753561bb05e86", "path": "LoginRequiredView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a7c4f4bb51ba6a3caf94cb186aaaa26d", "path": "MapboxLocationTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3268eb60e5dccb1af083b73b504ab52cd", "path": "UserAvatarView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb31482a5762901b77bd78aba2be98a2371", "path": "WeatherCardView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3716bc72fa4035492eed4baa28c28984d", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb31ec4dbc818cd28b3abdc9e17ec67399e", "path": "AchievementDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb380ad6768f1ef94e2033896e751945a97", "path": "AchievementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb390625e24a1d38244729f9f37bff7fe24", "path": "ActiveTaskCard.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e6a260d3a1441a9a4c5e078250a44c74", "path": "ActivityDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb35ecb8eaf89adfda1bfa9f130d396c288", "path": "ActivityMapView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb392cd461799cf83acb36ce1f98c71900c", "path": "ActivityView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3b8d6983a6c80b107380f37bb14699cc9", "path": "CreateActivityView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3c9930b3a78e74088d2e4478aac642bee", "path": "ExploreActivitiesView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "672d8c4e5e4501786b9bd984bab4cfb35c83dce695641c211d1988222bdb6204", "path": "ExploreActivitiesView.swift.backup", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb33381ab9915600af6744b8322dbaa9c03", "path": "HistoryTracksPanel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3dbf50d4753b5bfddf8a358c55f7173ac", "path": "HistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb36d7e7b115f80007a90e7c75bf4c31420", "path": "LocationPickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3249e6d44fda985723896a68b5aa0f119", "path": "MapboxMapView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fb7ed969c4e927fa0c7f5214dab4c82c", "path": "MapSelectionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb343df52485483818fbf2afa1c9c860d3c", "path": "PermissionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb325366419b78040a7e42a769ff6c4a54b", "path": "PhotoGalleryPanel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3cf5e0334781fa802e9fdbff96b35676a", "path": "RideEndConfirmationPanel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3c7a14bbd8fb991c39101130c3a0b92d0", "path": "RideSummaryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3cd3e59f48fa3e510eb70dabf770cbb48", "path": "RideView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb376e903440ea4c13a1770c2369d8e8c82", "path": "SettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3866549be0fad276d0f5d8adbf7e1c1c1", "path": "SimpleMapboxLocationPicker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3df49b5aa51183ddd3459b7bf09bc4495", "path": "StatsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb39fdd5f78676cbdfef25cf883b29b94b8", "path": "TaskDetailPanel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb37607775a95a6d54db7eff54a33c5edc1", "path": "UserProfileEditView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d6fc72c3be74cfad28b7eacbc8061a0c", "path": "UserProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3f147e05b48ba4c8b6793c569758a6ffe", "path": "WelcomeSignInView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb33bdc66823b348a69b728cb0679cc8d9a", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "text", "guid": "672d8c4e5e4501786b9bd984bab4cfb311cb3d7a2cd5f6766de9ecf48ed07fe4", "path": ".cursorrules", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fb499f242a5ca0d970695cc70c6e3afb", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb35757ea80dd1635a0917d0648f49e444a", "path": "CloudBaseAuthView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb38f4727df8968e32949b6a5a04d27925f", "path": "CloudBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3eee8a1886a7f20d3fce2bc3a685967bd", "path": "CloudKitMinimal.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ad3ee61f1b5ef71301e3162491510647", "path": "CloudKitUserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a5725e31c4080433a81fef0aa7cef772", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "672d8c4e5e4501786b9bd984bab4cfb31f78aef451d7a636e9ca918840c0d80a", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb3adec9b512c664a4ee3fbf30c7a950041", "path": "en.lproj/Localizable.strings", "regionVariantName": "en", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb3bbf67ecc013218934d9e453952eebfc6", "path": "da.lproj/Localizable.strings", "regionVariantName": "da", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb31eaa81f162494444ad7234eef1092413", "path": "de.lproj/Localizable.strings", "regionVariantName": "de", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb347010a2d1a9288989a758991c2bcaaa6", "path": "es.lproj/Localizable.strings", "regionVariantName": "es", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb3494e26080f52fee06f00a48002fccaad", "path": "fr.lproj/Localizable.strings", "regionVariantName": "fr", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb34a57027c439d05239d4db61a4e262948", "path": "it.lproj/Localizable.strings", "regionVariantName": "it", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb34cb76d14ee08c576e3fa77b70caa309b", "path": "ja.lproj/Localizable.strings", "regionVariantName": "ja", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e403d55e3f54946e2ebac3a3e852d995", "path": "ko.lproj/Localizable.strings", "regionVariantName": "ko", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb379510140f14b08906fdb28ed1067376c", "path": "nl.lproj/Localizable.strings", "regionVariantName": "nl", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.strings", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fe094db031fadcc993016ade67cc3fe7", "path": "zh-Hans.lproj/Localizable.strings", "regionVariantName": "zh-Hans", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb386c424ec4595b2c84e7042181501ef76", "name": "Localizable.strings", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb357731a9cab258d954204774890666538", "path": "Persistence.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "672d8c4e5e4501786b9bd984bab4cfb3f0033bf2f144fd9b0ca99fed36ce1c29", "path": "Pulse.entitlements", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcdatamodel", "guid": "672d8c4e5e4501786b9bd984bab4cfb3b5dd1adceff4ce5e2214f67dac36a985", "path": "Pulse.xcdatamodel", "sourceTree": "<group>", "type": "file"}], "fileType": "wrapper.xcdatamodeld", "guid": "672d8c4e5e4501786b9bd984bab4cfb3df52497be16485ea01fe51460d1a7afd", "name": "Pulse.xcdatamodeld", "path": "Pulse.xcdatamodeld", "sourceTree": "<group>", "type": "versionGroup"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb35fb52a95f318c0c4e001ea3468271ab5", "path": "PulseApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb30e6a740d0dfba60a45787f78043db0ee", "name": "Pulse", "path": "Pulse", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a9368bca7a28c2ae90d3c179d3a47535", "path": "PulseTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb328e364072e07829217b6479e829e12f8", "name": "PulseTests", "path": "PulseTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb370f803a24f7e69053ea9dad56c42ae14", "path": "PulseUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3c1f31fc7b5ecf6f2f4002a832108fac4", "path": "PulseUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3db54603087eb0fa48ea2ed12da98da37", "name": "PulseUITests", "path": "PulseUITests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "672d8c4e5e4501786b9bd984bab4cfb39249faa78e051a6429b7d4ab36495fe2", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "672d8c4e5e4501786b9bd984bab4cfb33f5c41ab4be5108bcdaa955fea3fedce", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3631d2cec578c29cb97ca8061d854c851", "path": "PulseColors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a3162e437723596813899e2ad8f66f4c", "path": "PulseStatsProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb304e0353b2fbc333c7f6051bd85519b70", "path": "PulseStatsWidgetView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb34790aa9fd63f0e30d8696edb9800dfb0", "path": "PulseWidget.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fe00f1e7158debb3a3b17a2b94c04e1c", "path": "PulseWidgetLiveActivity.swift", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3089f33a0527329e3c1ec579b23998a7d", "name": "PulseWidget", "path": "PulseWidget", "sourceTree": "<group>", "type": "group"}, {"guid": "672d8c4e5e4501786b9bd984bab4cfb339aaccb704b239823b7c7777105f9911", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "672d8c4e5e4501786b9bd984bab4cfb31237dc0571d411260d7cfcca9b379ea5", "path": "Target Support Files/Pods-Pulse/Pods-Pulse.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ac3c0591b5374eb60d622c3469f33a3f", "path": "Target Support Files/Pods-Pulse/Pods-Pulse.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "672d8c4e5e4501786b9bd984bab4cfb3424b2221738dce3c94b3c7f75bfdd40b", "path": "Target Support Files/Pods-Pulse-PulseUITests/Pods-Pulse-PulseUITests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "672d8c4e5e4501786b9bd984bab4cfb3242b23ff6a1e006bfe453c8d13d78b54", "path": "Target Support Files/Pods-Pulse-PulseUITests/Pods-Pulse-PulseUITests.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "672d8c4e5e4501786b9bd984bab4cfb317963dff8abefaff6ee9c07be32fcaa1", "path": "Target Support Files/Pods-PulseTests/Pods-PulseTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "672d8c4e5e4501786b9bd984bab4cfb395d7413a979612ca88385b17832257c2", "path": "Target Support Files/Pods-PulseTests/Pods-PulseTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb333d8c95139665f378e8ae1fb430af76a", "name": "Pods", "path": "Pods", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.framework", "guid": "672d8c4e5e4501786b9bd984bab4cfb397cb5d792d829ffa6f50e169c80e2e9c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/WidgetKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fb6cacf0c7ee4f02553f51b6940f0767", "path": "Pods_Pulse.framework", "sourceTree": "BUILT_PRODUCTS_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "672d8c4e5e4501786b9bd984bab4cfb39e313d1c601545cdba910b8953d2207e", "path": "Pods_Pulse_PulseUITests.framework", "sourceTree": "BUILT_PRODUCTS_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "672d8c4e5e4501786b9bd984bab4cfb331cbd274c0c91a9c8f742680f329c589", "path": "Pods_PulseTests.framework", "sourceTree": "BUILT_PRODUCTS_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "672d8c4e5e4501786b9bd984bab4cfb35b5029b97c9b6d0d3b4a0addc86592d3", "path": "System/Library/Frameworks/WidgetKit.framework", "sourceTree": "SDKROOT", "type": "file"}, {"fileType": "wrapper.framework", "guid": "672d8c4e5e4501786b9bd984bab4cfb3dd8dc41d273be3be974876d7414dce2b", "path": "System/Library/Frameworks/SwiftUI.framework", "sourceTree": "SDKROOT", "type": "file"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb32f44c91330db60cae57821a5cb6787ff", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3bd65cd075ca9556d49d0a6e11a2a23a1", "name": "Pulse", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb3", "path": "/Users/<USER>/workspace/myApps/Pulse/Pulse.xcodeproj", "projectDirectory": "/Users/<USER>/workspace/myApps/Pulse", "targets": ["TARGET@v11_hash=ac7446f5cb405937612f4c1f5ce86977", "TARGET@v11_hash=e16b57ccdc6c5d9580393dc48bc8617a", "TARGET@v11_hash=cb2dfc01ab5cfb13d1fd5ba5aa2b36d7", "TARGET@v11_hash=77b074c2caaf045b1a03b78b37d95eb6"]}