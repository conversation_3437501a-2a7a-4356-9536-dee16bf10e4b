{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME": "WidgetBackground", "CODE_SIGN_ENTITLEMENTS": "PulseWidgetExtension.entitlements", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "PulseWidget/Info.plist", "INFOPLIST_KEY_CFBundleDisplayName": "Pulse统计", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "IPHONEOS_DEPLOYMENT_TARGET": "17.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.pulsetrack.app.PulseWidget", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb313731f252ce108a5d7f1986d89d026e3", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME": "WidgetBackground", "CODE_SIGN_ENTITLEMENTS": "PulseWidgetExtension.entitlements", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "PulseWidget/Info.plist", "INFOPLIST_KEY_CFBundleDisplayName": "Pulse统计", "INFOPLIST_KEY_NSHumanReadableCopyright": "", "IPHONEOS_DEPLOYMENT_TARGET": "17.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @executable_path/../../Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.pulsetrack.app.PulseWidget", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb36e2e907648bee9575dd06667fca32e59", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3a3162e437723596813899e2ad8f66f4c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ff6e2760d03f1ac178c9ee216c25b910"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb304e0353b2fbc333c7f6051bd85519b70", "guid": "672d8c4e5e4501786b9bd984bab4cfb3af986b19cfc32ff1856cc88e20590b7a"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3631d2cec578c29cb97ca8061d854c851", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d0c206c82e17a0202b3d91f75f3d49ac"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3fe00f1e7158debb3a3b17a2b94c04e1c", "guid": "672d8c4e5e4501786b9bd984bab4cfb39f3bfdd86bbfde7740e78b09023d9945"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb34790aa9fd63f0e30d8696edb9800dfb0", "guid": "672d8c4e5e4501786b9bd984bab4cfb3be7b794bcca36cf056308b9453900ede"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3a015c9cd17995e77fd0541e676647e0f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3dd8dc41d273be3be974876d7414dce2b", "guid": "672d8c4e5e4501786b9bd984bab4cfb318f27db1e3f92e5fe169d0f526698ffb"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb35b5029b97c9b6d0d3b4a0addc86592d3", "guid": "672d8c4e5e4501786b9bd984bab4cfb31ca38c0264b9085a83a5f4aaab5f247b"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3cdbb9a75f6b8740ce0b738d2320bdd5a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb39249faa78e051a6429b7d4ab36495fe2", "guid": "672d8c4e5e4501786b9bd984bab4cfb331ec0678b25d5c07bd7feaf0036a4a6c"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3b2256a9e4ede1f502a02a8f22fa25abb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "672d8c4e5e4501786b9bd984bab4cfb38611578ef8cda81bdb3c0ca7ffbf8fcd", "name": "PulseWidgetExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "672d8c4e5e4501786b9bd984bab4cfb3eeb993be272863ed93802346f728c3b9", "name": "PulseWidgetExtension.appex", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.app-extension", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}