{"buildConfigurations": [{"baseConfigurationFileReference": "672d8c4e5e4501786b9bd984bab4cfb31237dc0571d411260d7cfcca9b379ea5", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "Pulse/Pulse.entitlements", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "ENABLE_HARDENED_RUNTIME": "YES", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "Pulse/Info.plist", "INFOPLIST_KEY_NSCameraUsageDescription": "此应用需要访问相机来拍摄骑行过程中的照片", "INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription": "Pulse需要访问您的位置信息来记录骑行路线，即使在后台运行时也能准确跟踪骑行轨迹", "INFOPLIST_KEY_NSLocationUsageDescription": "Pulse使用GPS定位来跟踪您的骑行路线和计算距离、速度等数据", "INFOPLIST_KEY_NSLocationWhenInUseUsageDescription": "Pulse需要访问您的位置信息来记录骑行路线和数据统计", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "17.6", "LD_RUNPATH_SEARCH_PATHS": "@executable_path/Frameworks", "LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]": "@executable_path/../Frameworks", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.pulsetrack.app", "PRODUCT_NAME": "$(TARGET_NAME)", "REGISTER_APP_GROUPS": "YES", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator", "SUPPORTS_MACCATALYST": "NO", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb323aa401c4d89a196913b0a8ef3ccc602", "name": "Debug"}, {"baseConfigurationFileReference": "672d8c4e5e4501786b9bd984bab4cfb3ac3c0591b5374eb60d622c3469f33a3f", "buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_ENTITLEMENTS": "Pulse/Pulse.entitlements", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "ENABLE_HARDENED_RUNTIME": "YES", "ENABLE_PREVIEWS": "YES", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "Pulse/Info.plist", "INFOPLIST_KEY_NSCameraUsageDescription": "此应用需要访问相机来拍摄骑行过程中的照片", "INFOPLIST_KEY_NSLocationAlwaysAndWhenInUseUsageDescription": "Pulse需要访问您的位置信息来记录骑行路线，即使在后台运行时也能准确跟踪骑行轨迹", "INFOPLIST_KEY_NSLocationUsageDescription": "Pulse使用GPS定位来跟踪您的骑行路线和计算距离、速度等数据", "INFOPLIST_KEY_NSLocationWhenInUseUsageDescription": "Pulse需要访问您的位置信息来记录骑行路线和数据统计", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]": "YES", "INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]": "YES", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]": "UIStatusBarStyleDefault", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "IPHONEOS_DEPLOYMENT_TARGET": "17.6", "LD_RUNPATH_SEARCH_PATHS": "@executable_path/Frameworks", "LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]": "@executable_path/../Frameworks", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.pulsetrack.app", "PRODUCT_NAME": "$(TARGET_NAME)", "REGISTER_APP_GROUPS": "YES", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator", "SUPPORTS_MACCATALYST": "NO", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb331ddc9ad8947c8c613d27b8d122836d7", "name": "Release"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "672d8c4e5e4501786b9bd984bab4cfb34f9d48ea669db9c43f177a240e47d20b", "inputFileListPaths": [], "inputFilePaths": ["${PODS_PODFILE_DIR_PATH}/Podfile.lock", "${PODS_ROOT}/Manifest.lock"], "name": "[CP] Check Pods Manifest.lock", "originalObjectID": "EE9899DD2850C0C4A443D06A", "outputFileListPaths": [], "outputFilePaths": ["$(DERIVED_FILE_DIR)/Pods-Pulse-checkManifestLockResult.txt"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3eddc3b1be1f7494f78c00ed34a1feaf2", "guid": "672d8c4e5e4501786b9bd984bab4cfb3577b322d564aa5b4f6544dedf955cd77"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb39fdd5f78676cbdfef25cf883b29b94b8", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a522ca7d8e6a098ae63dfe9561653a5e"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb32c3ff067b4e34efee47e86c3927a2bfe", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ba719f82ffec96138ee82df4375fd5e5"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3268eb60e5dccb1af083b73b504ab52cd", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a7e26644bebc7d1c3a0b9a4215843986"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3bcd23b0dbd078b5402292eaae47f32bc", "guid": "672d8c4e5e4501786b9bd984bab4cfb393a73ce172a9361105d7b1c71b778a4e"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb31ec4dbc818cd28b3abdc9e17ec67399e", "guid": "672d8c4e5e4501786b9bd984bab4cfb3151c679fd75e287c4a371d9fb84ce388"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb36d7e7b115f80007a90e7c75bf4c31420", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e77ad0ff0c8dc0f1b77c64625389894d"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb35757ea80dd1635a0917d0648f49e444a", "guid": "672d8c4e5e4501786b9bd984bab4cfb398cf587117b9b4f25c3b65f76c9b858a"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3111b87c93ec556e8391c93d4f0a88edb", "guid": "672d8c4e5e4501786b9bd984bab4cfb38f832e656164bbc9611d285d77c0baf8"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3df49b5aa51183ddd3459b7bf09bc4495", "guid": "672d8c4e5e4501786b9bd984bab4cfb36e4da573e9443dc7f51ca2e00ae2a161"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3eee8a1886a7f20d3fce2bc3a685967bd", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ab47f31220fa78279371567818ef8e07"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3a99084012685e980df87118536e66d2c", "guid": "672d8c4e5e4501786b9bd984bab4cfb354bc9660b1ef14002d90181098abae45"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb35e01c474e78729dd802579d5a2a1f385", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d84e8fea93e02f30518959d4d1cba925"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb325366419b78040a7e42a769ff6c4a54b", "guid": "672d8c4e5e4501786b9bd984bab4cfb346667edc07aa75636a1be6f967893418"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb31482a5762901b77bd78aba2be98a2371", "guid": "672d8c4e5e4501786b9bd984bab4cfb3586d80e968b54e359b039f03593148ac"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb38c8b47218905b001d209e2a4c282a161", "guid": "672d8c4e5e4501786b9bd984bab4cfb39f587d375cc918e629f48cf80577a404"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb35ecb8eaf89adfda1bfa9f130d396c288", "guid": "672d8c4e5e4501786b9bd984bab4cfb31a42b88eccfe762bc4e1896c187451fb"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3866549be0fad276d0f5d8adbf7e1c1c1", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a4f8a74b978ddf889172ef5838835f27"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb376e903440ea4c13a1770c2369d8e8c82", "guid": "672d8c4e5e4501786b9bd984bab4cfb3380a30e8f302907a6b101a6cb6908019"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3e6315f8ddaed751f18bf90546de7b89d", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d3012e9adef8401ddcd76870c987ccc4"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3ffc9660f50d54ccaa8089a503ac79b2a", "guid": "672d8c4e5e4501786b9bd984bab4cfb34bf540c765f1fe67dad2c23ced9886d9"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3dbf50d4753b5bfddf8a358c55f7173ac", "guid": "672d8c4e5e4501786b9bd984bab4cfb3243f214d9a2dfe775bcf129a7376c181"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3cd3e59f48fa3e510eb70dabf770cbb48", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a4bd86a903293b7c8ff42ba2acf07b10"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3c9930b3a78e74088d2e4478aac642bee", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a6779e10b18552a1ee5115320dae019b"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3b3b85ba9f081675988f6eb15f226da2c", "guid": "672d8c4e5e4501786b9bd984bab4cfb320b013ce2586bd41e774624d2b0f6ef4"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3c7a14bbd8fb991c39101130c3a0b92d0", "guid": "672d8c4e5e4501786b9bd984bab4cfb3c8105245dbb20d5dd1dec15b355b3b78"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3d6f47ca6df4aaaf537e4ebea97e94478", "guid": "672d8c4e5e4501786b9bd984bab4cfb326b55df4cf20dc4f882315de662c132c"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3e6a260d3a1441a9a4c5e078250a44c74", "guid": "672d8c4e5e4501786b9bd984bab4cfb385cf69e0626c03fadb614a859dae8e24"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb31eb0d723f466be3b9d10bc90ba11b67a", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a27ed2d79297392f150801bc9fa06298"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb35fb52a95f318c0c4e001ea3468271ab5", "guid": "672d8c4e5e4501786b9bd984bab4cfb33d143595b82b59088ef72d5bc7c68f16"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb37f4aee8775b842094dd06dc101709f19", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d7f0be5b72dca3938ff43bbad2370759"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3cf5e0334781fa802e9fdbff96b35676a", "guid": "672d8c4e5e4501786b9bd984bab4cfb369277f1d1880787ef7bffdaece2db253"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3a3637b8de3b0ef038f3a7e13c3698140", "guid": "672d8c4e5e4501786b9bd984bab4cfb34b51cd9f4845223269c78af544ca2dfd"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb369f8755335b5308e7a3f83644c1f0ec4", "guid": "672d8c4e5e4501786b9bd984bab4cfb36afd96ee6e1f00142c0adf9002e78d36"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb34ad68ccf9cc52dc6630f8e3af3c4a908", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ecee3ba3925e66d0e3d45b83d7f73d90"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb343df52485483818fbf2afa1c9c860d3c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3920523e0430195dd7ef21620ba2de7a9"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3f1ef76465a039df9cfb1c960bc4c1239", "guid": "672d8c4e5e4501786b9bd984bab4cfb3103c96e714a89a5bdf53b017fdc38c9d"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb355ecff637f9971d21bfebce753d3e11f", "guid": "672d8c4e5e4501786b9bd984bab4cfb38f7b405d91593262e67c4140a4fbce17"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb38f4727df8968e32949b6a5a04d27925f", "guid": "672d8c4e5e4501786b9bd984bab4cfb3417104d0d5811fa291c538b496b48483"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3fb7ed969c4e927fa0c7f5214dab4c82c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3dd64d1de2a9f0683f89a30773a2daa35"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb31cff990fc3a9fe40b6c29f98309dcdef", "guid": "672d8c4e5e4501786b9bd984bab4cfb34695884ab9f84b1230d8a009803507a3"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3a5725e31c4080433a81fef0aa7cef772", "guid": "672d8c4e5e4501786b9bd984bab4cfb31bf8753fbd55fa9e2e8b9e27842a0310"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb383dd8707d52bd5a104646cccd8a58cd7", "guid": "672d8c4e5e4501786b9bd984bab4cfb310e4ccffd6267a4859c2e341e3c1c801"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb390625e24a1d38244729f9f37bff7fe24", "guid": "672d8c4e5e4501786b9bd984bab4cfb37add6bab78f00bd0b88651f90d5c29cb"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3ca8cb7b4b6b9be8c21e30f574ac96eb7", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d0a197fe2d1e9bbc7dba4d88f7965577"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3b8d6983a6c80b107380f37bb14699cc9", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e1001ec423bca8e3df0a152cda5b3201"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3a7c4f4bb51ba6a3caf94cb186aaaa26d", "guid": "672d8c4e5e4501786b9bd984bab4cfb35c67ffd2ec74a27a4d88840318b9e6ce"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3df52497be16485ea01fe51460d1a7afd", "guid": "672d8c4e5e4501786b9bd984bab4cfb38ccf1992ad180cc35d2b3bc6c63e99e1"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3633ab938dd5f643115342af79a7ca464", "guid": "672d8c4e5e4501786b9bd984bab4cfb3b560a647fdc9d7a0c4b36164c7efe17b"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3d1718f095a79a53520e828a9a15f3181", "guid": "672d8c4e5e4501786b9bd984bab4cfb336edb2e6c9ba0dc70df1f36b4280984d"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3954503362c9daaad55d476594397856c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3e85dfbb25d290d96a13d2446d92ac4d9"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb310c8b92bc28a5c00a2c206da90a6c947", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ae8ff1375e9472047667e7d279d327ed"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3c22f853e64a2e2392c76ba76514adec6", "guid": "672d8c4e5e4501786b9bd984bab4cfb341f77855ba6d9c467ea0aa70041933c5"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3f147e05b48ba4c8b6793c569758a6ffe", "guid": "672d8c4e5e4501786b9bd984bab4cfb359890ff538858df8bc880207d82bac3a"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb380ad6768f1ef94e2033896e751945a97", "guid": "672d8c4e5e4501786b9bd984bab4cfb315f0a9c2d08b9f5ed96fb9830d06dcc8"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3249e6d44fda985723896a68b5aa0f119", "guid": "672d8c4e5e4501786b9bd984bab4cfb3575680745ae6299a7ee5bda81d94d0f4"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3fd48261e9c770b4e0dbce28e55f858f5", "guid": "672d8c4e5e4501786b9bd984bab4cfb3568b772febfc7211323b06dc660a77c7"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3ad3ee61f1b5ef71301e3162491510647", "guid": "672d8c4e5e4501786b9bd984bab4cfb3558910fb2fc2823e97a54d2faf7c5519"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3d6fc72c3be74cfad28b7eacbc8061a0c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3909bb13936cb0c033ee533d1f7f55e96"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb353901711a912427470d30be330b4e6b5", "guid": "672d8c4e5e4501786b9bd984bab4cfb318d46ccc80b95f2c454737595d187604"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb33381ab9915600af6744b8322dbaa9c03", "guid": "672d8c4e5e4501786b9bd984bab4cfb31c2e4886147d7afb4c52a8fe6486a5db"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb34fde1aeddbc5c86ca8e0468282dbd6ee", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d087e5626e6a4d1aaf420eaf4a452f39"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3e0387467942fa921b4ae02896cb20c82", "guid": "672d8c4e5e4501786b9bd984bab4cfb327ece011b0e7241e99d74e4a4675aa5a"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb392cd461799cf83acb36ce1f98c71900c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3f22f6ef58cdad70f830ff1d2e7c35aaa"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb37607775a95a6d54db7eff54a33c5edc1", "guid": "672d8c4e5e4501786b9bd984bab4cfb394037371709ffffa5e39d8f73ff94d1e"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb340420861e2d02812541753561bb05e86", "guid": "672d8c4e5e4501786b9bd984bab4cfb3716971fc4c5a548e042618304ab956f7"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb30051070370057cad288c985254d146ca", "guid": "672d8c4e5e4501786b9bd984bab4cfb3dd0980e4a15970523b731b12391301fb"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb357731a9cab258d954204774890666538", "guid": "672d8c4e5e4501786b9bd984bab4cfb365633f083d9e210c39454be4f968e605"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb36e797e43b17ff16e14982373825b2c4e", "guid": "672d8c4e5e4501786b9bd984bab4cfb3fb8a023b61c095daaa55d97af843922c"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb30079c8bf81be48362c468f4a74e05444", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb397cb5d792d829ffa6f50e169c80e2e9c", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ccaf0e9ec89c2162aeb1d591e7ce5662"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3746bcb433658af265a44b19ae37bc10a", "guid": "672d8c4e5e4501786b9bd984bab4cfb3aeccdedcc8d73956044fcd20f4628e84"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb388ec06513fa07201213cee51a9b92b7f", "guid": "672d8c4e5e4501786b9bd984bab4cfb311717e4fb7fc4104c2c10675c1d292ee"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3d35f32bcae81cb717b08a42a0f15ebea", "guid": "672d8c4e5e4501786b9bd984bab4cfb32fc08be68cb62bd3f189e29e2e15fa04"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb38cd781d45425ed88122387f65c67e7bf", "guid": "672d8c4e5e4501786b9bd984bab4cfb35df758c83aa66d83bfe86c8e5ad8e30c"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3e6ce9b17bfdf0d0e85af110f2585f385", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3dfdbb6503fd66d5e82111124abf0a953", "guid": "672d8c4e5e4501786b9bd984bab4cfb3684b5b243f2b90b49b752fe5550586b1"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb32f0aa35e9bab9bb933191ee81b0e9c6f", "guid": "672d8c4e5e4501786b9bd984bab4cfb399a9989ad3ec0bc4629909c12a13309e"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb386c424ec4595b2c84e7042181501ef76", "guid": "672d8c4e5e4501786b9bd984bab4cfb3a843767dc3e6360de80864636d73c307"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb35c83dce695641c211d1988222bdb6204", "guid": "672d8c4e5e4501786b9bd984bab4cfb39253f29f881b8561381d6b39fd8f4f2b"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb311cb3d7a2cd5f6766de9ecf48ed07fe4", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ffb07a613649ec51d62df3f3b1382cc0"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3fb499f242a5ca0d970695cc70c6e3afb", "guid": "672d8c4e5e4501786b9bd984bab4cfb34a439fbe1b819e92f668e59bce1452e1"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb38c703de060951d1b6313450a2763b657", "guid": "672d8c4e5e4501786b9bd984bab4cfb32e9fe76ca17ba24d23b19b8aa462a458"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3fa9e59a18577554fdc36381be9401d82", "type": "com.apple.buildphase.resources"}, {"buildFiles": [{"codeSignOnCopy": "true", "fileReference": "672d8c4e5e4501786b9bd984bab4cfb3746bcb433658af265a44b19ae37bc10a", "guid": "672d8c4e5e4501786b9bd984bab4cfb31cb0bf62ee26efdb342ccb75b31aa451", "removeHeadersOnCopy": "true"}, {"codeSignOnCopy": "true", "fileReference": "672d8c4e5e4501786b9bd984bab4cfb3d35f32bcae81cb717b08a42a0f15ebea", "guid": "672d8c4e5e4501786b9bd984bab4cfb303b1c712edad21f7915c16e581edb951", "removeHeadersOnCopy": "true"}, {"codeSignOnCopy": "true", "fileReference": "672d8c4e5e4501786b9bd984bab4cfb38cd781d45425ed88122387f65c67e7bf", "guid": "672d8c4e5e4501786b9bd984bab4cfb36318d3e6e8276b227d03be66390ae3da", "removeHeadersOnCopy": "true"}, {"codeSignOnCopy": "true", "fileReference": "672d8c4e5e4501786b9bd984bab4cfb388ec06513fa07201213cee51a9b92b7f", "guid": "672d8c4e5e4501786b9bd984bab4cfb3ccd32ff1201726bc5a81ecf6536bedd6", "removeHeadersOnCopy": "true"}], "destinationSubfolder": "$(FRAMEWORKS_FOLDER_PATH)", "destinationSubpath": "", "guid": "672d8c4e5e4501786b9bd984bab4cfb3b373bcbae7cb629845290bd1452db192", "type": "com.apple.buildphase.copy-files"}, {"buildFiles": [{"guid": "672d8c4e5e4501786b9bd984bab4cfb38e08e810eed1fa277a67f9ffe7c73e2d", "removeHeadersOnCopy": "true", "targetReference": "672d8c4e5e4501786b9bd984bab4cfb38611578ef8cda81bdb3c0ca7ffbf8fcd"}], "destinationSubfolder": "$(PLUGINS_FOLDER_PATH)", "destinationSubpath": "", "guid": "672d8c4e5e4501786b9bd984bab4cfb334ff7fbb00e7e2a9cfb0364286f0719d", "type": "com.apple.buildphase.copy-files"}], "buildRules": [], "dependencies": [{"guid": "672d8c4e5e4501786b9bd984bab4cfb38611578ef8cda81bdb3c0ca7ffbf8fcd", "name": "PulseWidgetExtension"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb36b3a5087a6d2344ceee063750cf51df7", "name": "Pulse", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "672d8c4e5e4501786b9bd984bab4cfb39f27b7dc257895b672b0312d46ee6016", "name": "Pulse.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}