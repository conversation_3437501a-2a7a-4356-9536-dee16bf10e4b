{"buildConfigurations": [{"baseConfigurationFileReference": "672d8c4e5e4501786b9bd984bab4cfb3424b2221738dce3c94b3c7f75bfdd40b", "buildSettings": {"CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.PulseUITests", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator macosx xros xrsimulator", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2,7", "TEST_TARGET_NAME": "Pulse", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb3d6afb4723e579a51e678b8f34e250755", "name": "Debug"}, {"baseConfigurationFileReference": "672d8c4e5e4501786b9bd984bab4cfb3242b23ff6a1e006bfe453c8d13d78b54", "buildSettings": {"CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.PulseUITests", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator macosx xros xrsimulator", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2,7", "TEST_TARGET_NAME": "Pulse", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb3e42599989ee7d5f61d0a260c652d28d3", "name": "Release"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "672d8c4e5e4501786b9bd984bab4cfb3566f1c8a85608e4ef50e39652ea1bf09", "inputFileListPaths": [], "inputFilePaths": ["${PODS_PODFILE_DIR_PATH}/Podfile.lock", "${PODS_ROOT}/Manifest.lock"], "name": "[CP] Check Pods Manifest.lock", "originalObjectID": "E818992732F139EEF878831B", "outputFileListPaths": [], "outputFilePaths": ["$(DERIVED_FILE_DIR)/Pods-Pulse-PulseUITests-checkManifestLockResult.txt"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3c1f31fc7b5ecf6f2f4002a832108fac4", "guid": "672d8c4e5e4501786b9bd984bab4cfb3af92c3ba219f9873655c6b34956a6b97"}, {"fileReference": "672d8c4e5e4501786b9bd984bab4cfb370f803a24f7e69053ea9dad56c42ae14", "guid": "672d8c4e5e4501786b9bd984bab4cfb38a444311fbfcae6b725f54b5ba14769d"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3786b1acf7061b7d40f5a300e6a66b78e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb39e313d1c601545cdba910b8953d2207e", "guid": "672d8c4e5e4501786b9bd984bab4cfb3d70e4df7bc83de1de9816221368bb95b"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3a4b34777f5ad1bc1f327d80ec09fb436", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "672d8c4e5e4501786b9bd984bab4cfb3c287e3a571f9f9685ec9b94048e0e9d0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "672d8c4e5e4501786b9bd984bab4cfb36b3a5087a6d2344ceee063750cf51df7", "name": "Pulse"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3517ca28f70df45df5c0b2abf50dc0de3", "name": "PulseUITests", "performanceTestsBaselinesPath": "/Users/<USER>/workspace/myApps/Pulse/Pulse.xcodeproj/xcshareddata/xcbaselines/7B72EC692DF02B0A00B2EE76.xcbaseline", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "672d8c4e5e4501786b9bd984bab4cfb3ec4727001def64b449827822d48cde6e", "name": "PulseUITests.xctest", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle.ui-testing", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}