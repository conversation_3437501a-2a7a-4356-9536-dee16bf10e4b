{"buildConfigurations": [{"baseConfigurationFileReference": "672d8c4e5e4501786b9bd984bab4cfb317963dff8abefaff6ee9c07be32fcaa1", "buildSettings": {"BUNDLE_LOADER": "$(TEST_HOST)", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.PulseTests", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator macosx xros xrsimulator", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2,7", "TEST_HOST": "$(BUILT_PRODUCTS_DIR)/Pulse.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Pulse", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb3676cd90d4e374a0efb556a98a9a76c9a", "name": "Debug"}, {"baseConfigurationFileReference": "672d8c4e5e4501786b9bd984bab4cfb395d7413a979612ca88385b17832257c2", "buildSettings": {"BUNDLE_LOADER": "$(TEST_HOST)", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "R37WQ24K3U", "GENERATE_INFOPLIST_FILE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.5", "MACOSX_DEPLOYMENT_TARGET": "15.5", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.PulseTests", "PRODUCT_NAME": "$(TARGET_NAME)", "SDKROOT": "auto", "SUPPORTED_PLATFORMS": "iphoneos iphonesimulator macosx xros xrsimulator", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2,7", "TEST_HOST": "$(BUILT_PRODUCTS_DIR)/Pulse.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Pulse", "XROS_DEPLOYMENT_TARGET": "2.5"}, "guid": "672d8c4e5e4501786b9bd984bab4cfb3fd87b1e924095ee7e30d280984f644d8", "name": "Release"}], "buildPhases": [{"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "false", "guid": "672d8c4e5e4501786b9bd984bab4cfb3063f9933a14ce80e275849b16b8127b1", "inputFileListPaths": [], "inputFilePaths": ["${PODS_PODFILE_DIR_PATH}/Podfile.lock", "${PODS_ROOT}/Manifest.lock"], "name": "[CP] Check Pods Manifest.lock", "originalObjectID": "D7CE56DAF8F74D9161C61F0C", "outputFileListPaths": [], "outputFilePaths": ["$(DERIVED_FILE_DIR)/Pods-PulseTests-checkManifestLockResult.txt"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb3a9368bca7a28c2ae90d3c179d3a47535", "guid": "672d8c4e5e4501786b9bd984bab4cfb3598a7d561e5442f7e3308635d3a68664"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb388d79587253a4ce306546c3ee9bc46f3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "672d8c4e5e4501786b9bd984bab4cfb331cbd274c0c91a9c8f742680f329c589", "guid": "672d8c4e5e4501786b9bd984bab4cfb3edf939cd4d8d17fd79ea24f624978a5f"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb3d2a76bc00f2d48135b9b00bbdcf937db", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "672d8c4e5e4501786b9bd984bab4cfb346f1595fa5158b5aba2535b08a2be33c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "672d8c4e5e4501786b9bd984bab4cfb36b3a5087a6d2344ceee063750cf51df7", "name": "Pulse"}], "guid": "672d8c4e5e4501786b9bd984bab4cfb318099befd0a236d49759936a400f53ac", "name": "PulseTests", "performanceTestsBaselinesPath": "/Users/<USER>/workspace/myApps/Pulse/Pulse.xcodeproj/xcshareddata/xcbaselines/7B72EC5F2DF02B0A00B2EE76.xcbaseline", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "672d8c4e5e4501786b9bd984bab4cfb343f75287bcefe0efe7b40702c07da18f", "name": "PulseTests.xctest", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle.unit-test", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}