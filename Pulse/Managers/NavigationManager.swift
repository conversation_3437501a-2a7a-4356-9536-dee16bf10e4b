import Foundation
import MapKit
import CoreLocation

// MARK: - 导航管理器
class NavigationManager: ObservableObject {
    @Published var isNavigating = false
    @Published var currentRoute: ExploreActivity?
    @Published var navigationError: String?
    
    // MARK: - 开始导航
    func startNavigation(for route: ExploreActivity) {
        guard !route.waypoints.isEmpty else {
            navigationError = "路线没有有效的途径点"
            return
        }
        
        currentRoute = route
        isNavigating = true
        navigationError = nil
        
        // 使用第一个途径点作为目的地开始导航
        let firstWaypoint = route.waypoints[0]
        openInMaps(waypoint: firstWaypoint, routeTitle: route.title)
    }
    
    // MARK: - 导航到特定途径点
    func navigateToWaypoint(_ waypoint: Waypoint, routeTitle: String = "") {
        openInMaps(waypoint: waypoint, routeTitle: routeTitle)
    }
    
    // MARK: - 在系统地图中打开导航
    private func openInMaps(waypoint: Waypoint, routeTitle: String) {
        let coordinate = CLLocationCoordinate2D(
            latitude: waypoint.latitude,
            longitude: waypoint.longitude
        )
        
        let placemark = MKPlacemark(coordinate: coordinate)
        let mapItem = MKMapItem(placemark: placemark)
        mapItem.name = waypoint.name
        
        // 设置启动选项
        let launchOptions: [String: Any] = [
            MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeWalking, // 使用步行模式，最接近骑行
            MKLaunchOptionsShowsTrafficKey: true,
            MKLaunchOptionsMapTypeKey: MKMapType.standard.rawValue
        ]
        
        // 打开系统地图进行导航
        mapItem.openInMaps(launchOptions: launchOptions)
        
        print("🧭 开始导航到: \(waypoint.name)")
        
        // 延迟重置导航状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.isNavigating = false
        }
    }
    
    // MARK: - 停止导航
    func stopNavigation() {
        isNavigating = false
        currentRoute = nil
        navigationError = nil
        print("🛑 导航已停止")
    }
    
    // MARK: - 获取路线总览
    func getRouteOverview(for route: ExploreActivity) -> RouteOverview {
        return RouteOverview(
            totalDistance: route.distance,
            estimatedDuration: route.duration,
            difficulty: route.difficulty,
            waypointCount: route.waypoints.count,
            highlights: route.highlights
        )
    }
    
    // MARK: - 检查导航可用性
    func canNavigate(for route: ExploreActivity) -> Bool {
        return !route.waypoints.isEmpty && 
               route.waypoints.allSatisfy { waypoint in
                   waypoint.latitude != 0 && waypoint.longitude != 0
               }
    }
    
    // MARK: - 生成导航指令
    func generateNavigationInstructions(for route: ExploreActivity) -> [NavigationInstruction] {
        var instructions: [NavigationInstruction] = []
        
        // 起点指令
        if let firstWaypoint = route.waypoints.first {
            instructions.append(NavigationInstruction(
                type: .start,
                description: "从 \(firstWaypoint.name) 开始骑行",
                waypoint: firstWaypoint,
                distance: 0
            ))
        }
        
        // 途径点指令
        for (index, waypoint) in route.waypoints.enumerated() {
            if index > 0 {
                let previousWaypoint = route.waypoints[index - 1]
                let distance = calculateDistance(
                    from: previousWaypoint,
                    to: waypoint
                )
                
                instructions.append(NavigationInstruction(
                    type: .waypoint,
                    description: "到达 \(waypoint.name)",
                    waypoint: waypoint,
                    distance: distance
                ))
            }
        }
        
        // 终点指令
        if let lastWaypoint = route.waypoints.last, route.waypoints.count > 1 {
            instructions.append(NavigationInstruction(
                type: .finish,
                description: "到达终点 \(lastWaypoint.name)",
                waypoint: lastWaypoint,
                distance: 0
            ))
        }
        
        return instructions
    }
    
    // MARK: - 计算两点间距离
    private func calculateDistance(from: Waypoint, to: Waypoint) -> Double {
        let fromLocation = CLLocation(latitude: from.latitude, longitude: from.longitude)
        let toLocation = CLLocation(latitude: to.latitude, longitude: to.longitude)
        return fromLocation.distance(from: toLocation) / 1000.0 // 转换为公里
    }
}

// MARK: - 支持数据结构

// 路线总览
struct RouteOverview {
    let totalDistance: Double
    let estimatedDuration: String
    let difficulty: String
    let waypointCount: Int
    let highlights: [String]
    
    var formattedDistance: String {
        return String(format: "%.1f km", totalDistance)
    }
    
    var difficultyLevel: DifficultyLevel {
        switch difficulty.lowercased() {
        case "简单", "easy":
            return .easy
        case "中等", "medium":
            return .medium
        case "困难", "hard":
            return .hard
        case "专业", "expert":
            return .expert
        default:
            return .medium
        }
    }
}

// 难度等级
enum DifficultyLevel: String, CaseIterable {
    case easy = "简单"
    case medium = "中等"
    case hard = "困难"
    case expert = "专业"
    
    var color: String {
        switch self {
        case .easy:
            return "green"
        case .medium:
            return "orange"
        case .hard:
            return "red"
        case .expert:
            return "purple"
        }
    }
    
    var icon: String {
        switch self {
        case .easy:
            return "leaf.fill"
        case .medium:
            return "speedometer"
        case .hard:
            return "flame.fill"
        case .expert:
            return "crown.fill"
        }
    }
}

// 导航指令
struct NavigationInstruction {
    let type: InstructionType
    let description: String
    let waypoint: Waypoint
    let distance: Double // 从上一个点到这个点的距离（公里）
    
    var formattedDistance: String {
        if distance < 0.1 {
            return "起点"
        } else {
            return String(format: "%.1f km", distance)
        }
    }
}

// 指令类型
enum InstructionType {
    case start      // 起点
    case waypoint   // 途径点
    case finish     // 终点
    
    var icon: String {
        switch self {
        case .start:
            return "play.circle.fill"
        case .waypoint:
            return "location.circle.fill"
        case .finish:
            return "checkmark.circle.fill"
        }
    }
    
    var color: String {
        switch self {
        case .start:
            return "green"
        case .waypoint:
            return "blue"
        case .finish:
            return "red"
        }
    }
}

// MARK: - 导航辅助功能

extension NavigationManager {
    
    // MARK: - 获取最近的途径点
    func getNearestWaypoint(to location: CLLocation, in route: ExploreActivity) -> Waypoint? {
        return route.waypoints.min { waypoint1, waypoint2 in
            let location1 = CLLocation(latitude: waypoint1.latitude, longitude: waypoint1.longitude)
            let location2 = CLLocation(latitude: waypoint2.latitude, longitude: waypoint2.longitude)
            
            return location.distance(from: location1) < location.distance(from: location2)
        }
    }
    
    // MARK: - 检查是否到达途径点
    func isNearWaypoint(_ waypoint: Waypoint, userLocation: CLLocation, threshold: Double = 50.0) -> Bool {
        let waypointLocation = CLLocation(latitude: waypoint.latitude, longitude: waypoint.longitude)
        return userLocation.distance(from: waypointLocation) <= threshold
    }
    
    // MARK: - 获取下一个途径点
    func getNextWaypoint(after currentWaypoint: Waypoint, in route: ExploreActivity) -> Waypoint? {
        guard let currentIndex = route.waypoints.firstIndex(where: { $0.id == currentWaypoint.id }),
              currentIndex + 1 < route.waypoints.count else {
            return nil
        }
        
        return route.waypoints[currentIndex + 1]
    }
    
    // MARK: - 计算路线进度
    func calculateProgress(userLocation: CLLocation, in route: ExploreActivity) -> Double {
        guard !route.waypoints.isEmpty else { return 0.0 }
        
        let nearestWaypoint = getNearestWaypoint(to: userLocation, in: route)
        guard let waypoint = nearestWaypoint,
              let waypointIndex = route.waypoints.firstIndex(where: { $0.id == waypoint.id }) else {
            return 0.0
        }
        
        return Double(waypointIndex) / Double(route.waypoints.count - 1)
    }
}
