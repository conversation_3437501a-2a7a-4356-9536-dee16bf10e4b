import Foundation
import CoreLocation
import UIKit

// MARK: - 探索活动数据模型
struct ExploreActivity: Identifiable, Codable {
    let id: String
    let title: String
    let description: String
    let distance: Double // 距离(公里)
    let duration: String // 骑行时间
    let difficulty: String // 难度等级
    let highlights: [String] // 亮点特色
    let roadCondition: String // 路面状况
    let bestTime: String // 最佳骑行时间
    let waypoints: [Waypoint] // 途径点信息（用于生成真实路线）
    let gpxTrack: [ExploreTrackPoint] // GPX轨迹点（从Apple Maps生成）
    let city: String // 所在城市
    let createdDate: Date
    var mapSnapshot: UIImage? // 地图快照（不参与Codable）
    
    // 轨迹点的便捷访问
    var trackPoints: [ExploreTrackPoint] {
        return gpxTrack
    }
    
    // 计算属性
    var mapboxGeoJSON: [String: Any] {
        return ExploreGPXParser.convertToMapboxGeoJSON(trackPoints: gpxTrack)
    }
    
    var centerCoordinate: CLLocationCoordinate2D? {
        guard !gpxTrack.isEmpty else { return nil }
        
        let latitudes = gpxTrack.map { $0.latitude }
        let longitudes = gpxTrack.map { $0.longitude }
        
        let avgLat = latitudes.reduce(0, +) / Double(latitudes.count)
        let avgLon = longitudes.reduce(0, +) / Double(longitudes.count)
        
        return CLLocationCoordinate2D(latitude: avgLat, longitude: avgLon)
    }
    
    var boundingBox: (southwest: CLLocationCoordinate2D, northeast: CLLocationCoordinate2D)? {
        guard !gpxTrack.isEmpty else { return nil }
        
        let latitudes = gpxTrack.map { $0.latitude }
        let longitudes = gpxTrack.map { $0.longitude }
        
        guard let minLat = latitudes.min(), let maxLat = latitudes.max(),
              let minLon = longitudes.min(), let maxLon = longitudes.max() else {
            return nil
        }
        
        let southwest = CLLocationCoordinate2D(latitude: minLat, longitude: minLon)
        let northeast = CLLocationCoordinate2D(latitude: maxLat, longitude: maxLon)
        
        return (southwest: southwest, northeast: northeast)
    }
    
    init(id: String = UUID().uuidString, title: String, description: String, distance: Double, duration: String, difficulty: String, highlights: [String], roadCondition: String, bestTime: String, waypoints: [Waypoint], gpxTrack: [ExploreTrackPoint] = [], city: String) {
        self.id = id
        self.title = title
        self.description = description
        self.distance = distance
        self.duration = duration
        self.difficulty = difficulty
        self.highlights = highlights
        self.roadCondition = roadCondition
        self.bestTime = bestTime
        self.waypoints = waypoints
        self.gpxTrack = gpxTrack
        self.city = city
        self.createdDate = Date()
        self.mapSnapshot = nil
    }
    
    // MARK: - Codable实现 (mapSnapshot不参与编码/解码)
    enum CodingKeys: String, CodingKey {
        case id, title, description, distance, duration, difficulty
        case highlights, roadCondition, bestTime, waypoints, gpxTrack, city, createdDate
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        title = try container.decode(String.self, forKey: .title)
        description = try container.decode(String.self, forKey: .description)
        distance = try container.decode(Double.self, forKey: .distance)
        duration = try container.decode(String.self, forKey: .duration)
        difficulty = try container.decode(String.self, forKey: .difficulty)
        highlights = try container.decode([String].self, forKey: .highlights)
        roadCondition = try container.decode(String.self, forKey: .roadCondition)
        bestTime = try container.decode(String.self, forKey: .bestTime)
        waypoints = try container.decode([Waypoint].self, forKey: .waypoints)
        gpxTrack = try container.decode([ExploreTrackPoint].self, forKey: .gpxTrack)
        city = try container.decode(String.self, forKey: .city)
        createdDate = try container.decode(Date.self, forKey: .createdDate)
        mapSnapshot = nil // 不从JSON解码，运行时生成
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(title, forKey: .title)
        try container.encode(description, forKey: .description)
        try container.encode(distance, forKey: .distance)
        try container.encode(duration, forKey: .duration)
        try container.encode(difficulty, forKey: .difficulty)
        try container.encode(highlights, forKey: .highlights)
        try container.encode(roadCondition, forKey: .roadCondition)
        try container.encode(bestTime, forKey: .bestTime)
        try container.encode(waypoints, forKey: .waypoints)
        try container.encode(gpxTrack, forKey: .gpxTrack)
        try container.encode(city, forKey: .city)
        try container.encode(createdDate, forKey: .createdDate)
        // mapSnapshot 不编码到JSON
    }
}

// MARK: - GPX轨迹点
struct ExploreTrackPoint: Identifiable, Codable {
    let id: String
    let latitude: Double
    let longitude: Double
    let elevation: Double?
    let name: String?
    
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }
    
    init(latitude: Double, longitude: Double, elevation: Double? = nil, name: String? = nil) {
        self.id = UUID().uuidString
        self.latitude = latitude
        self.longitude = longitude
        self.elevation = elevation
        self.name = name
    }
}

// MARK: - DeepSeek API响应模型
struct DeepSeekRouteResponse: Codable {
    let route: RouteInfo
    let gpxData: String
}

struct RouteInfo: Codable {
    let name: String
    let distance: Double
    let duration: String
    let difficulty: String
    let highlights: [String]
    let roadCondition: String
    let bestTime: String
    let description: String
}

// MARK: - 途径点模型
struct Waypoint: Identifiable, Codable {
    let id: String
    let name: String
    let address: String
    let description: String
    let type: String
    let latitude: Double
    let longitude: Double

    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }

    init(id: String = UUID().uuidString, name: String, address: String = "", description: String = "", type: String = "waypoint", latitude: Double, longitude: Double) {
        self.id = id
        self.name = name
        self.address = address
        self.description = description
        self.type = type
        self.latitude = latitude
        self.longitude = longitude
    }

    // 从简单的 Waypoint 创建完整的 Waypoint（用于兼容现有API）
    init(from simpleWaypoint: SimpleWaypoint, latitude: Double, longitude: Double) {
        self.id = UUID().uuidString
        self.name = simpleWaypoint.name
        self.address = simpleWaypoint.address
        self.description = simpleWaypoint.description
        self.type = simpleWaypoint.type
        self.latitude = latitude
        self.longitude = longitude
    }
}

// MARK: - 简单途径点模型（用于API响应）
struct SimpleWaypoint: Codable {
    let name: String
    let address: String
    let description: String
    let type: String
}

// MARK: - GPX解析器
class ExploreGPXParser {
    static func parseGPXString(_ gpxString: String) -> [ExploreTrackPoint] {
        var trackPoints: [ExploreTrackPoint] = []
        
        // 使用简单的字符串匹配解析GPX
        let lines = gpxString.components(separatedBy: .newlines)
        
        for line in lines {
            if line.contains("<trkpt") {
                // 安全地提取经纬度
                if let latRange = line.range(of: "lat=\""),
                   let latEndRange = line.range(of: "\"", range: latRange.upperBound..<line.endIndex),
                   let lonRange = line.range(of: "lon=\""),
                   let lonEndRange = line.range(of: "\"", range: lonRange.upperBound..<line.endIndex) {
                    
                    // 添加边界检查
                    guard latRange.upperBound < latEndRange.lowerBound,
                          lonRange.upperBound < lonEndRange.lowerBound,
                          latEndRange.lowerBound <= line.endIndex,
                          lonEndRange.lowerBound <= line.endIndex else {
                        print("⚠️ GPX解析：坐标提取边界检查失败")
                        continue
                    }
                    
                    let latString = String(line[latRange.upperBound..<latEndRange.lowerBound])
                    let lonString = String(line[lonRange.upperBound..<lonEndRange.lowerBound])
                    
                    if let lat = Double(latString), let lon = Double(lonString) {
                        // 提取海拔和名称（如果有）
                        var elevation: Double? = nil
                        var name: String? = nil
                        
                        // 安全地寻找对应的ele和name标签
                        if let currentLineIndex = lines.firstIndex(of: line) {
                            for i in (currentLineIndex + 1)..<min(currentLineIndex + 3, lines.count) {
                                let nextLine = lines[i]
                                
                                if nextLine.contains("<ele>") {
                                    let eleValue = nextLine.replacingOccurrences(of: "<ele>", with: "")
                                        .replacingOccurrences(of: "</ele>", with: "")
                                        .trimmingCharacters(in: .whitespaces)
                                    elevation = Double(eleValue)
                                }
                                
                                if nextLine.contains("<n>") {
                                    name = nextLine.replacingOccurrences(of: "<n>", with: "")
                                        .replacingOccurrences(of: "</n>", with: "")
                                        .trimmingCharacters(in: .whitespaces)
                                }
                            }
                        }
                        
                        let trackPoint = ExploreTrackPoint(latitude: lat, longitude: lon, elevation: elevation, name: name)
                        trackPoints.append(trackPoint)
                    }
                }
            }
        }
        
        return trackPoints
    }
    
    static func convertToMapboxGeoJSON(trackPoints: [ExploreTrackPoint]) -> [String: Any] {
        let coordinates = trackPoints.map { [$0.longitude, $0.latitude] }
        
        return [
            "type": "Feature",
            "geometry": [
                "type": "LineString",
                "coordinates": coordinates
            ],
            "properties": [
                "stroke": "#00D9FF", // Pulse主题色
                "stroke-width": 3,
                "stroke-opacity": 0.8
            ]
        ]
    }
} 