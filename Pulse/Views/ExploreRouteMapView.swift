import SwiftUI
import MapKit
import CoreLocation

// MARK: - 探索路线地图视图
struct ExploreRouteMapView: View {
    let route: ExploreActivity
    let themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss
    @StateObject private var mapViewModel = ExploreRouteMapViewModel()
    @State private var showingNavigationAlert = false
    @State private var isNavigating = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // 地图视图
                ExploreMapView(
                    route: route,
                    mapViewModel: mapViewModel,
                    themeManager: themeManager
                )
                .ignoresSafeArea(.container, edges: .bottom)
                
                // 顶部信息卡片
                VStack {
                    topInfoCard
                    Spacer()
                }
                .padding(.top, 10)
                
                // 底部控制面板
                VStack {
                    Spacer()
                    bottomControlPanel
                }
            }
            .navigationTitle("路线地图")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        mapViewModel.toggleMapType()
                    }) {
                        Image(systemName: mapViewModel.mapType == .standard ? "map.fill" : "satellite.fill")
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    }
                }
            }
        }
        .alert("开始导航", isPresented: $showingNavigationAlert) {
            Button("取消", role: .cancel) { }
            Button("确认") {
                startNavigation()
            }
        } message: {
            Text("是否要开始沿此路线导航？这将打开系统地图应用进行导航。")
        }
        .onAppear {
            mapViewModel.setupRoute(route)
        }
    }
    
    // MARK: - 顶部信息卡片
    private var topInfoCard: some View {
        HStack(spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                Text(route.title)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(themeManager.isDarkMode ? .white : .black)
                    .lineLimit(1)
                
                HStack(spacing: 16) {
                    HStack(spacing: 4) {
                        Image(systemName: "road.lanes")
                            .font(.system(size: 12))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        
                        Text("\(String(format: "%.1f", route.distance)) km")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                    }
                    
                    HStack(spacing: 4) {
                        Image(systemName: "clock.fill")
                            .font(.system(size: 12))
                            .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        
                        Text(route.duration)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.8) : .secondary)
                    }
                }
            }
            
            Spacer()
            
            // 当前位置按钮
            Button(action: {
                mapViewModel.centerOnUserLocation()
            }) {
                Image(systemName: "location.fill")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.pulseAccent(isDarkMode: themeManager.isDarkMode))
                    .frame(width: 36, height: 36)
                    .background(
                        Circle()
                            .fill(.ultraThinMaterial)
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    )
            }
            .buttonStyle(.plain)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .padding(.horizontal, 16)
    }
    
    // MARK: - 底部控制面板
    private var bottomControlPanel: some View {
        VStack(spacing: 12) {
            // 途径点信息
            if !route.waypoints.isEmpty {
                waypointInfoView
            }
            
            // 导航按钮
            navigationButton
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.1), radius: 12, x: 0, y: -4)
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 10)
    }
    
    // MARK: - 途径点信息视图
    private var waypointInfoView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(Array(route.waypoints.enumerated()), id: \.offset) { index, waypoint in
                    WaypointInfoCard(
                        waypoint: waypoint,
                        index: index,
                        themeManager: themeManager,
                        onTap: {
                            mapViewModel.focusOnWaypoint(waypoint)
                        }
                    )
                }
            }
            .padding(.horizontal, 4)
        }
    }
    
    // MARK: - 导航按钮
    private var navigationButton: some View {
        Button(action: {
            showingNavigationAlert = true
        }) {
            HStack(spacing: 12) {
                if isNavigating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "location.north.fill")
                        .font(.system(size: 18, weight: .semibold))
                }
                
                Text(isNavigating ? "导航中..." : "开始导航")
                    .font(.system(size: 18, weight: .semibold))
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 54)
            .background(
                LinearGradient(
                    colors: [
                        Color.pulseAccent(isDarkMode: themeManager.isDarkMode),
                        Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.8)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .clipShape(RoundedRectangle(cornerRadius: 16))
            .shadow(color: Color.pulseAccent(isDarkMode: themeManager.isDarkMode).opacity(0.3), radius: 12, x: 0, y: 6)
        }
        .buttonStyle(.plain)
        .disabled(isNavigating)
    }
    
    // MARK: - 导航功能
    private func startNavigation() {
        guard !route.waypoints.isEmpty else { return }
        
        isNavigating = true
        
        // 使用第一个途径点作为目的地
        let firstWaypoint = route.waypoints[0]
        let coordinate = CLLocationCoordinate2D(
            latitude: firstWaypoint.latitude,
            longitude: firstWaypoint.longitude
        )
        
        let placemark = MKPlacemark(coordinate: coordinate)
        let mapItem = MKMapItem(placemark: placemark)
        mapItem.name = firstWaypoint.name
        
        // 打开系统地图进行导航
        mapItem.openInMaps(launchOptions: [
            MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeWalking, // 使用步行模式，最接近骑行
            MKLaunchOptionsShowsTrafficKey: true
        ])
        
        // 延迟重置状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isNavigating = false
        }
    }
}

// MARK: - MapKit 地图视图
struct ExploreMapView: UIViewRepresentable {
    let route: ExploreActivity
    let mapViewModel: ExploreRouteMapViewModel
    let themeManager: ThemeManager
    
    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        mapView.mapType = mapViewModel.mapType
        
        // 设置地图样式
        if themeManager.isDarkMode {
            mapView.overrideUserInterfaceStyle = .dark
        } else {
            mapView.overrideUserInterfaceStyle = .light
        }
        
        return mapView
    }
    
    func updateUIView(_ mapView: MKMapView, context: Context) {
        mapView.mapType = mapViewModel.mapType
        
        // 清除现有的标注和覆盖层
        mapView.removeAnnotations(mapView.annotations.filter { !($0 is MKUserLocation) })
        mapView.removeOverlays(mapView.overlays)
        
        // 添加途径点标注
        for (index, waypoint) in route.waypoints.enumerated() {
            let annotation = WaypointAnnotation(waypoint: waypoint, index: index)
            mapView.addAnnotation(annotation)
        }
        
        // 添加路线覆盖层
        if !route.gpxTrack.isEmpty {
            let coordinates = route.gpxTrack.map { 
                CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) 
            }
            let polyline = MKPolyline(coordinates: coordinates, count: coordinates.count)
            mapView.addOverlay(polyline)
        }
        
        // 设置地图区域
        if !route.gpxTrack.isEmpty {
            let coordinates = route.gpxTrack.map { 
                CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) 
            }
            let region = calculateMapRegion(for: coordinates)
            mapView.setRegion(region, animated: true)
        } else if !route.waypoints.isEmpty {
            let coordinates = route.waypoints.map { 
                CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude) 
            }
            let region = calculateMapRegion(for: coordinates)
            mapView.setRegion(region, animated: true)
        }
        
        // 处理地图操作
        if mapViewModel.shouldCenterOnUser {
            mapView.setUserTrackingMode(.none, animated: true)
            if let userLocation = mapView.userLocation.location {
                let region = MKCoordinateRegion(
                    center: userLocation.coordinate,
                    latitudinalMeters: 1000,
                    longitudinalMeters: 1000
                )
                mapView.setRegion(region, animated: true)
            }
            mapViewModel.shouldCenterOnUser = false
        }
        
        if let focusWaypoint = mapViewModel.focusWaypoint {
            let coordinate = CLLocationCoordinate2D(
                latitude: focusWaypoint.latitude,
                longitude: focusWaypoint.longitude
            )
            let region = MKCoordinateRegion(
                center: coordinate,
                latitudinalMeters: 500,
                longitudinalMeters: 500
            )
            mapView.setRegion(region, animated: true)
            mapViewModel.focusWaypoint = nil
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    private func calculateMapRegion(for coordinates: [CLLocationCoordinate2D]) -> MKCoordinateRegion {
        guard !coordinates.isEmpty else {
            return MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 39.9042, longitude: 116.4074),
                latitudinalMeters: 5000,
                longitudinalMeters: 5000
            )
        }
        
        let latitudes = coordinates.map { $0.latitude }
        let longitudes = coordinates.map { $0.longitude }
        
        let minLat = latitudes.min()!
        let maxLat = latitudes.max()!
        let minLon = longitudes.min()!
        let maxLon = longitudes.max()!
        
        let center = CLLocationCoordinate2D(
            latitude: (minLat + maxLat) / 2,
            longitude: (minLon + maxLon) / 2
        )
        
        let span = MKCoordinateSpan(
            latitudeDelta: max((maxLat - minLat) * 1.3, 0.01),
            longitudeDelta: max((maxLon - minLon) * 1.3, 0.01)
        )
        
        return MKCoordinateRegion(center: center, span: span)
    }
    
    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: ExploreMapView

        init(_ parent: ExploreMapView) {
            self.parent = parent
        }
        
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            if let polyline = overlay as? MKPolyline {
                let renderer = MKPolylineRenderer(polyline: polyline)
                renderer.strokeColor = UIColor(Color.pulseAccent(isDarkMode: parent.themeManager.isDarkMode))
                renderer.lineWidth = 4
                return renderer
            }
            return MKOverlayRenderer()
        }
        
        func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
            if annotation is MKUserLocation {
                return nil
            }
            
            if let waypointAnnotation = annotation as? WaypointAnnotation {
                let identifier = "WaypointAnnotation"
                var annotationView = mapView.dequeueReusableAnnotationView(withIdentifier: identifier)
                
                if annotationView == nil {
                    annotationView = MKAnnotationView(annotation: annotation, reuseIdentifier: identifier)
                    annotationView?.canShowCallout = true
                }
                
                annotationView?.annotation = annotation
                
                // 创建自定义标注视图
                let pinView = WaypointPinView(
                    index: waypointAnnotation.index,
                    themeManager: parent.themeManager
                )
                let hostingController = UIHostingController(rootView: pinView)
                hostingController.view.backgroundColor = .clear
                annotationView?.addSubview(hostingController.view)
                
                hostingController.view.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    hostingController.view.centerXAnchor.constraint(equalTo: annotationView!.centerXAnchor),
                    hostingController.view.centerYAnchor.constraint(equalTo: annotationView!.centerYAnchor),
                    hostingController.view.widthAnchor.constraint(equalToConstant: 30),
                    hostingController.view.heightAnchor.constraint(equalToConstant: 30)
                ])
                
                return annotationView
            }
            
            return nil
        }
    }
}

// MARK: - 支持组件和模型

// 地图视图模型
class ExploreRouteMapViewModel: ObservableObject {
    @Published var mapType: MKMapType = .standard
    @Published var shouldCenterOnUser = false
    @Published var focusWaypoint: Waypoint?
    
    private var route: ExploreActivity?
    
    func setupRoute(_ route: ExploreActivity) {
        self.route = route
    }
    
    func toggleMapType() {
        mapType = mapType == .standard ? .satellite : .standard
    }
    
    func centerOnUserLocation() {
        shouldCenterOnUser = true
    }
    
    func focusOnWaypoint(_ waypoint: Waypoint) {
        focusWaypoint = waypoint
    }
}

// 途径点标注
class WaypointAnnotation: NSObject, MKAnnotation {
    let waypoint: Waypoint
    let index: Int
    
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: waypoint.latitude, longitude: waypoint.longitude)
    }
    
    var title: String? {
        return waypoint.name
    }
    
    var subtitle: String? {
        return waypoint.description
    }
    
    init(waypoint: Waypoint, index: Int) {
        self.waypoint = waypoint
        self.index = index
    }
}

// 途径点信息卡片
struct WaypointInfoCard: View {
    let waypoint: Waypoint
    let index: Int
    let themeManager: ThemeManager
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 8) {
                // 序号
                ZStack {
                    Circle()
                        .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode))
                        .frame(width: 20, height: 20)
                    
                    Text("\(index + 1)")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(waypoint.name)
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(themeManager.isDarkMode ? .white : .black)
                        .lineLimit(1)
                    
                    if !waypoint.description.isEmpty {
                        Text(waypoint.description)
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(themeManager.isDarkMode ? .white.opacity(0.7) : .secondary)
                            .lineLimit(1)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(.ultraThinMaterial.opacity(0.8))
            )
        }
        .buttonStyle(.plain)
    }
}

// 途径点标注视图
struct WaypointPinView: View {
    let index: Int
    let themeManager: ThemeManager
    
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.pulseAccent(isDarkMode: themeManager.isDarkMode))
                .frame(width: 30, height: 30)
                .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            
            Text("\(index + 1)")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
        }
    }
}
